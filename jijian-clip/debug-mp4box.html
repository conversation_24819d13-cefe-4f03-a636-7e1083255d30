<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MP4Box 调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
    </style>
</head>
<body>
    <h1>MP4Box 调试测试</h1>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>点击选择MP4文件进行测试</p>
        <input type="file" id="fileInput" accept="video/*" style="display: none;">
    </div>
    
    <button onclick="clearLog()">清空日志</button>
    
    <div id="log" class="log"></div>

    <script src="https://unpkg.com/mp4box@0.5.2/dist/mp4box.all.min.js"></script>
    <script>
        function log(message) {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function testMP4File(file) {
            log(`🎬 开始测试文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
            
            if (!window.MP4Box) {
                log('❌ MP4Box 不可用');
                return;
            }

            try {
                const arrayBuffer = await file.arrayBuffer();
                log(`📁 文件读取完成，大小: ${arrayBuffer.byteLength} bytes`);
                
                const mp4boxFile = MP4Box.createFile();
                log('✅ MP4Box 文件对象创建成功');

                // 设置回调
                mp4boxFile.onError = (error) => {
                    log(`❌ MP4Box 错误: ${error}`);
                };

                mp4boxFile.onReady = (info) => {
                    log('🎯 MP4Box onReady 触发');
                    log(`📊 文件信息: ${JSON.stringify({
                        duration: info.duration,
                        timescale: info.timescale,
                        videoTracks: info.videoTracks?.length || 0,
                        audioTracks: info.audioTracks?.length || 0
                    }, null, 2)}`);

                    if (info.videoTracks && info.videoTracks.length > 0) {
                        const track = info.videoTracks[0];
                        log(`🎥 视频轨道信息:`);
                        log(`  - ID: ${track.id}`);
                        log(`  - 编码: ${track.codec}`);
                        log(`  - 样本数: ${track.nb_samples}`);
                        log(`  - 时长: ${track.duration}`);
                        log(`  - 时间刻度: ${track.timescale}`);
                        log(`  - 分辨率: ${track.track_width}x${track.track_height}`);
                        
                        // 检查关键的索引表
                        log(`🔍 索引表检查:`);
                        log(`  - 有samples: ${!!(track.samples && track.samples.length > 0)}`);
                        log(`  - samples长度: ${track.samples ? track.samples.length : 0}`);
                        log(`  - 有stsc: ${!!(track.stsc && track.stsc.entries && track.stsc.entries.length > 0)}`);
                        log(`  - stsc条目数: ${track.stsc && track.stsc.entries ? track.stsc.entries.length : 0}`);
                        log(`  - 有stco: ${!!(track.stco && track.stco.entries && track.stco.entries.length > 0)}`);
                        log(`  - stco条目数: ${track.stco && track.stco.entries ? track.stco.entries.length : 0}`);
                        log(`  - 有co64: ${!!(track.co64 && track.co64.entries && track.co64.entries.length > 0)}`);

                        // 尝试获取完整track信息
                        try {
                            const fullTrack = mp4boxFile.getTrackById(track.id);
                            log(`🔍 完整track信息:`);
                            log(`  - 完整track存在: ${!!fullTrack}`);
                            if (fullTrack) {
                                log(`  - 完整track samples: ${fullTrack.samples ? fullTrack.samples.length : 0}`);
                                log(`  - 完整track stsc: ${fullTrack.stsc && fullTrack.stsc.entries ? fullTrack.stsc.entries.length : 0}`);
                                log(`  - 完整track stco: ${fullTrack.stco && fullTrack.stco.entries ? fullTrack.stco.entries.length : 0}`);
                            }
                        } catch (fullTrackError) {
                            log(`❌ 获取完整track失败: ${fullTrackError.message}`);
                        }

                        // 检查第一个sample的数据
                        if (track.samples && track.samples.length > 0) {
                            const firstSample = track.samples[0];
                            log(`🔍 第一个sample检查:`);
                            log(`  - 有data: ${!!firstSample.data}`);
                            log(`  - data类型: ${typeof firstSample.data}`);
                            log(`  - data长度: ${firstSample.data ? firstSample.data.byteLength : 'undefined'}`);
                            log(`  - 有cts: ${firstSample.cts !== undefined}`);
                            log(`  - cts值: ${firstSample.cts}`);
                            log(`  - 有is_sync: ${firstSample.is_sync !== undefined}`);
                            log(`  - is_sync值: ${firstSample.is_sync}`);
                        }

                        // 尝试设置提取选项
                        try {
                            log('🔧 尝试设置提取选项...');
                            mp4boxFile.setExtractionOptions(track.id, null, {
                                nbSamples: 1
                            });
                            log('✅ 提取选项设置成功');

                            // 尝试启动
                            log('🚀 尝试启动MP4Box...');
                            mp4boxFile.start();
                            log('✅ MP4Box 启动成功');
                        } catch (startError) {
                            log(`❌ MP4Box 启动失败: ${startError.message}`);
                            log(`❌ 错误堆栈: ${startError.stack}`);
                        }
                    }
                };

                mp4boxFile.onSamples = (id, user, samples) => {
                    log(`🎯 收到samples: track=${id}, 数量=${samples.length}`);
                    if (samples.length > 0) {
                        const sample = samples[0];
                        log(`  - 第一个sample data: ${!!sample.data} (${sample.data ? sample.data.byteLength : 0} bytes)`);
                    }
                };

                // 追加数据
                log('📤 追加数据到MP4Box...');
                (arrayBuffer as any).fileStart = 0;
                mp4boxFile.appendBuffer(arrayBuffer);
                
                log('🔄 触发flush...');
                mp4boxFile.flush();
                
                log('✅ 测试完成');

            } catch (error) {
                log(`❌ 测试失败: ${error.message}`);
                log(`❌ 错误堆栈: ${error.stack}`);
            }
        }

        // 文件选择事件
        document.getElementById('fileInput').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                clearLog();
                testMP4File(file);
            }
        });

        // 页面加载时检查MP4Box
        window.addEventListener('load', () => {
            if (window.MP4Box) {
                log('✅ MP4Box 已加载');
                log(`MP4Box 方法: ${Object.getOwnPropertyNames(window.MP4Box).join(', ')}`);
            } else {
                log('❌ MP4Box 未加载');
            }
        });
    </script>
</body>
</html>
