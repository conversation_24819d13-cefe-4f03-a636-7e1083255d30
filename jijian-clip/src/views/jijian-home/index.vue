<template>
  <!-- 完全复制源项目的Home页面结构 -->
  <div class="min-h-screen bg-[var(--bg-light)]">
    <!-- Header组件 -->
    <JijianHeader
      :is-authenticated="userStore.isLoggedIn"
      @login-click="showLoginModal = true"
    />

    <!-- 登录弹窗 -->
    <JijianLoginModal
      v-model="showLoginModal"
      @login-success="handleLoginSuccess"
    />

    <main class="main-content pt-24">
      <!-- Hero Section - 完全复制源项目 -->
      <section class="relative overflow-hidden bg-gradient-to-b from-white to-[var(--bg-light)] py-16 md:py-24">
        <div class="absolute top-0 right-0 w-1/2 h-full bg-[var(--primary)]/5 rounded-l-full -z-10"></div>

        <!-- 🎯 优化后的浮动装饰图标 - 使用GPU加速 -->
        <div class="floating-icon floating-icon-1 absolute top-20 left-10 w-16 h-16 bg-[var(--primary)]/10 rounded-full flex items-center justify-center">
          <i class="fa-solid fa-video text-[var(--primary)] text-2xl"></i>
        </div>
        <div class="floating-icon floating-icon-2 absolute top-40 right-20 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
          <i class="fa-solid fa-magic-wand-sparkles text-blue-500 text-lg"></i>
        </div>
        <div class="floating-icon floating-icon-3 absolute bottom-40 left-20 w-14 h-14 bg-green-100 rounded-full flex items-center justify-center">
          <i class="fa-solid fa-scissors text-green-500 text-xl"></i>
        </div>
        <div class="floating-icon floating-icon-4 absolute top-60 right-40 w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
          <i class="fa-solid fa-sparkles text-yellow-500"></i>
        </div>
        <div class="floating-icon floating-icon-5 absolute bottom-60 right-10 w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center">
          <i class="fa-solid fa-wand-magic text-pink-500 text-lg"></i>
        </div>
        <div class="max-w-7xl mx-auto px-4 relative z-10">
          <FadeInSection :delay="0.1">
            <div class="text-center max-w-4xl mx-auto mb-16">
              <h1 class="font-bold mb-8 leading-tight text-[var(--text-dark)]" style="font-size: 3.75rem;">
                用<span class="text-gradient">对话</span>创造精彩视频
              </h1>
              <p class="text-[var(--text-light)] mb-12" style="font-size: 1.25rem; line-height: 1.75rem;">
                即剪AI让视频创作变得前所未有的简单。只需输入文字描述，AI就能帮你剪辑出专业级视频。
              </p>
              <div class="flex flex-col sm:flex-row justify-center gap-4">
                <button
                  class="px-8 py-3 bg-gradient-primary text-white rounded-lg text-lg font-medium shadow-[var(--shadow)] hover:shadow-xl transition-all transform hover:-translate-y-1 hover:scale-105"
                  @click="handleTryFree"
                >
                  免费试用 <i class="fa-solid fa-arrow-right ml-2"></i>
                </button>
                <button
                  class="px-8 py-3 bg-white text-[var(--text-dark)] rounded-lg text-lg font-medium shadow-[var(--shadow)] hover:shadow-xl transition-all flex items-center justify-center hover:scale-105 border border-gray-200 hover:border-[var(--primary)]"
                  @click="handleWatchDemo"
                >
                  <i class="fa-solid fa-play-circle mr-2 text-[var(--primary)]"></i> 观看演示
                </button>
              </div>
            </div>
          </FadeInSection>

          <FadeInSection :delay="0.3">
            <VideoShowcase @play-demo="handleWatchDemo" />
          </FadeInSection>
        </div>
      </section>

      <!-- 特色功能展示区 -->
      <FeatureCards />

      <!-- 数据统计展示区 -->
      <div class="max-w-7xl mx-auto px-4 py-8">
        <StatsSection :delay="0.5" />
      </div>

      <!-- 视频示例展示区 -->
      <section class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4">
          <FadeInSection :delay="0.1">
            <div class="text-center mb-16">
              <h2 class="font-bold text-[var(--text-dark)] mb-6" style="font-size: 3.75rem;">
                AI剪辑<span class="text-gradient">作品展示</span>
              </h2>
              <p class="text-[var(--text-light)] max-w-3xl mx-auto" style="font-size: 1.25rem; line-height: 1.75rem;">
                看看其他用户如何使用即剪AI创造出令人惊艳的视频作品
              </p>
            </div>
          </FadeInSection>

          <FadeInSection :delay="0.3">
            <VideoGrid @video-click="handleVideoClick" />
          </FadeInSection>
        </div>
      </section>

      <div class="max-w-7xl mx-auto px-4 py-16">
        <!-- 用户痛点和视频编辑功能 - 完全复制源项目布局 -->
        <FadeInSection :delay="0.1">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            <div class="lg:col-span-2">
              <UserPainPoints />
            </div>
            <div class="lg:col-span-1">
              <VideoEditingFeature />
            </div>
          </div>
        </FadeInSection>

        <!-- 解决方案展示 - 完全复制源项目 -->
        <FadeInSection :delay="0.2">
          <div class="mb-16">
            <h2 class="text-3xl font-bold text-center mb-12">AI视频创作新体验</h2>
            <SolutionsShowcase />
          </div>
        </FadeInSection>

        <!-- 项目展示区 - 完全复制源项目 -->
        <FadeInSection :delay="0.3">
          <div class="mb-16">
            <ProjectsSection />
          </div>
        </FadeInSection>

        <!-- 功能介绍区 - 完全复制源项目 -->
        <FadeInSection :delay="0.4">
          <div>
            <FeaturesSection />
          </div>
        </FadeInSection>

        <!-- CTA Section -->
        <CTASection
          :delay="0.5"
          @primary-action="handleGetStarted"
          @secondary-action="handleWatchDemo"
        />
      </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-100 py-12 mt-16">
      <div class="max-w-7xl mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div>
            <div class="logo text-2xl font-bold mb-4">
              <span class="text-gradient">即剪AI</span>
            </div>
            <p class="text-[var(--text-light)] mb-4">用对话创造精彩视频，AI驱动的视频剪辑革命。</p>
            <div class="flex space-x-4">
              <a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i class="fa-brands fa-twitter"></i></a>
              <a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i class="fa-brands fa-instagram"></i></a>
              <a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i class="fa-brands fa-youtube"></i></a>
              <a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors"><i class="fa-brands fa-github"></i></a>
            </div>
          </div>
          
          <div>
            <h3 class="font-bold text-lg mb-4">产品</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">功能</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">定价</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">教程</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">更新日志</a></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-bold text-lg mb-4">公司</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">关于我们</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">联系方式</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">招贤纳士</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">媒体资源</a></li>
            </ul>
          </div>
          
          <div>
            <h3 class="font-bold text-lg mb-4">法律</h3>
            <ul class="space-y-2">
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">隐私政策</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">服务条款</a></li>
              <li><a href="#" class="text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">版权声明</a></li>
            </ul>
          </div>
        </div>
        
        <div class="pt-8 border-t border-gray-100 flex flex-col md:flex-row justify-between items-center">
          <div class="text-sm text-[var(--text-light)] mb-4 md:mb-0">
            © 2023 即剪AI. 保留所有权利.
          </div>
          <div class="flex space-x-6">
            <a href="#" class="text-sm text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">隐私政策</a>
            <a href="#" class="text-sm text-[var(--text-light)] hover:text-[var(--primary)] transition-colors">服务条款</a>
          </div>
        </div>
      </div>
    </footer>

    <!-- 🎯 性能监控组件 -->
    <PerformanceMonitor :enabled="true" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineComponent, h, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/modules/user'
import { ElMessage } from 'element-plus'

// 导入组件 - 完全复制源项目结构
import JijianHeader from '@/components/jijian-ui/JijianHeader.vue'
import InputSection from '@/components/jijian-ui/InputSection.vue'
import UserPainPoints from '@/components/UserPainPoints.vue'
import SolutionsShowcase from '@/components/SolutionsShowcase.vue'
import VideoEditingFeature from '@/components/VideoEditingFeature.vue'
import ProjectsSection from '@/components/ProjectsSection.vue'
import FeaturesSection from '@/components/FeaturesSection.vue'
import JijianLoginModal from '@/components/jijian-ui/JijianLoginModal.vue'

// 新的组件化模块
import FeatureCards from '@/components/FeatureCards.vue'
import StatsSection from '@/components/StatsSection.vue'
import CTASection from '@/components/CTASection.vue'
import FadeInSection from '@/components/FadeInSection.vue'
import VideoShowcase from '@/components/VideoShowcase.vue'
import VideoGrid from '@/components/VideoGrid.vue'
import PerformanceMonitor from '@/components/PerformanceMonitor.vue'



// 组合式API
const router = useRouter()
const { t } = useI18n()
const userStore = useUserStore()

// 响应式数据
const showLoginModal = ref(false)

// 🎯 性能检测和动画优化
const devicePerformance = ref({
  isLowEnd: false,
  isMobile: false,
  cores: navigator.hardwareConcurrency || 4,
  memory: (navigator as any).deviceMemory || 4,
  connection: null as any
})

// 检测设备性能
const detectDevicePerformance = () => {
  // 检测移动设备
  devicePerformance.value.isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)

  // 检测网络连接
  const connection = (navigator as any).connection
  if (connection) {
    devicePerformance.value.connection = connection.effectiveType
  }

  // 综合判断是否为低端设备
  const isSlowConnection = connection && (
    connection.effectiveType === 'slow-2g' ||
    connection.effectiveType === '2g' ||
    connection.effectiveType === '3g'
  )

  devicePerformance.value.isLowEnd = (
    devicePerformance.value.cores <= 2 ||
    devicePerformance.value.memory <= 2 ||
    isSlowConnection ||
    (devicePerformance.value.isMobile && window.innerWidth <= 480)
  )

  console.log('🔍 设备性能检测:', devicePerformance.value)

  // 根据性能调整动画
  adjustAnimationsForDevice()
}

// 根据设备性能调整动画
const adjustAnimationsForDevice = () => {
  const root = document.documentElement

  if (devicePerformance.value.isLowEnd) {
    console.log('⚡ 检测到低端设备，启用性能优化模式')

    // 添加低性能设备的CSS类
    root.classList.add('low-performance-device')

    // 减少动画数量
    const floatingIcons = document.querySelectorAll('.floating-icon')
    floatingIcons.forEach((icon, index) => {
      if (index >= 3) { // 只保留前3个图标的动画
        (icon as HTMLElement).style.animation = 'none'
      }
    })

    // 简化FadeInSection动画
    const fadeInSections = document.querySelectorAll('.fade-in-section')
    fadeInSections.forEach(section => {
      (section as HTMLElement).style.transitionDuration = '0.3s'
    })

  } else if (devicePerformance.value.cores >= 8 && devicePerformance.value.memory >= 8) {
    console.log('🚀 检测到高性能设备，启用增强动画模式')

    // 添加高性能设备的CSS类
    root.classList.add('high-performance-device')
  }

  // 移动设备特殊处理
  if (devicePerformance.value.isMobile) {
    root.classList.add('mobile-device')

    // 减少动画复杂度
    const style = document.createElement('style')
    style.textContent = `
      .mobile-device .floating-icon {
        animation-duration: 8s !important;
        animation-timing-function: ease-in-out !important;
      }
      .mobile-device .fade-in-section {
        transition-duration: 0.4s !important;
      }
    `
    document.head.appendChild(style)
  }
}

// 方法 - 完全复制源项目逻辑
const handleTryFree = () => {
  console.log('点击免费试用按钮，跳转到上传页面')
  // 直接跳转到上传页面（测试环境）
  router.push('/upload')

  // 生产环境逻辑（暂时注释）
  // if (userStore.isLoggedIn) {
  //   router.push('/upload')
  // } else {
  //   showLoginModal.value = true
  // }
}

const handleWatchDemo = () => {
  console.log('点击观看演示按钮')
  // 滚动到视频展示区域
  const videoSection = document.querySelector('.video-showcase')
  if (videoSection) {
    videoSection.scrollIntoView({ behavior: 'smooth' })
  } else {
    // 如果没有找到视频展示区，滚动到演示区域
    const demoSection = document.querySelector('.solutions-showcase')
    if (demoSection) {
      demoSection.scrollIntoView({ behavior: 'smooth' })
    } else {
      console.log('未找到演示区域')
    }
  }
}

const handleVideoClick = (video: any) => {
  console.log('点击视频:', video.title)
  // 可以在这里添加视频播放逻辑或跳转到详情页
  ElMessage.success(`即将播放: ${video.title}`)
}

const handleGetStarted = () => {
  console.log('点击开始使用按钮，跳转到上传页面')
  // 直接跳转到上传页面（测试环境）
  router.push('/upload')

  // 生产环境逻辑（暂时注释）
  // if (userStore.isLoggedIn) {
  //   router.push('/upload')
  // } else {
  //   showLoginModal.value = true
  // }
}

const handleLoginSuccess = () => {
  userStore.setLoginStatus(true)
  showLoginModal.value = false
  ElMessage.success('登录成功！')
}

// 🎯 组件挂载时进行性能检测
onMounted(() => {
  // 延迟执行性能检测，避免影响首屏渲染
  setTimeout(() => {
    detectDevicePerformance()
  }, 100)

  // 监听网络状态变化
  if ('connection' in navigator) {
    const connection = (navigator as any).connection
    connection.addEventListener('change', () => {
      console.log('🌐 网络状态变化:', connection.effectiveType)
      detectDevicePerformance()
    })
  }

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    // 防抖处理
    clearTimeout(window.resizeTimer)
    window.resizeTimer = setTimeout(() => {
      detectDevicePerformance()
    }, 300)
  })
})
</script>

<style scoped>
/* 🎯 移除重复的fade-in-section样式 - 已在FadeInSection组件中定义 */

.text-gradient {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}

/* 🎯 优化的浮动装饰图标动画 - GPU加速版本 */
.floating-icon {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* 使用transform3d进行GPU加速的浮动动画 */
@keyframes optimizedFloat {
  0%, 100% {
    transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
  }
  50% {
    transform: translate3d(0, -10px, 0) scale3d(1.05, 1.05, 1);
  }
}

/* 为每个图标设置不同的动画延迟和持续时间 */
.floating-icon-1 {
  animation: optimizedFloat 4s ease-in-out infinite;
  animation-delay: 0s;
}

.floating-icon-2 {
  animation: optimizedFloat 3.5s ease-in-out infinite;
  animation-delay: 0.8s;
}

.floating-icon-3 {
  animation: optimizedFloat 4.2s ease-in-out infinite;
  animation-delay: 1.6s;
}

.floating-icon-4 {
  animation: optimizedFloat 3.8s ease-in-out infinite;
  animation-delay: 2.4s;
}

.floating-icon-5 {
  animation: optimizedFloat 4.1s ease-in-out infinite;
  animation-delay: 3.2s;
}

/* 减少动画在低性能设备上的影响 */
@media (prefers-reduced-motion: reduce) {
  .floating-icon {
    animation: none;
    transform: none;
  }
}

/* 🎯 响应式动画优化 */

/* 小屏幕设备优化 */
@media (max-width: 768px) {
  .floating-icon {
    animation-duration: 8s; /* 减慢动画速度 */
  }

  @keyframes optimizedFloat {
    0%, 100% {
      transform: translate3d(0, 0, 0);
    }
    50% {
      transform: translate3d(0, -5px, 0); /* 减少移动距离 */
    }
  }
}

/* 超小屏幕设备进一步简化 */
@media (max-width: 480px) {
  .floating-icon {
    animation-duration: 10s;
  }

  /* 隐藏部分装饰图标以提升性能 */
  .floating-icon-4,
  .floating-icon-5 {
    display: none;
  }
}

/* 低端设备检测和优化 */
@media (max-width: 768px) and (max-height: 1024px) {
  /* 可能是移动设备，进一步优化 */
  .floating-icon {
    animation-duration: 12s;
    animation-timing-function: linear; /* 使用更简单的时间函数 */
  }
}

/* 高分辨率设备优化 */
@media (min-width: 1920px) and (min-height: 1080px) {
  .floating-icon {
    animation-duration: 3s; /* 高性能设备可以使用更快的动画 */
  }

  @keyframes optimizedFloat {
    0%, 100% {
      transform: translate3d(0, 0, 0) scale3d(1, 1, 1);
    }
    50% {
      transform: translate3d(0, -15px, 0) scale3d(1.08, 1.08, 1); /* 增强效果 */
    }
  }
}

/* 暗色模式下的动画调整 */
@media (prefers-color-scheme: dark) {
  .floating-icon {
    filter: brightness(0.9); /* 稍微降低亮度 */
  }
}

/* 高对比度模式下禁用复杂动画 */
@media (prefers-contrast: high) {
  .floating-icon {
    animation: none;
    transform: none;
  }
}

/* 透明度偏好设置 */
@media (prefers-reduced-transparency: reduce) {
  .floating-icon {
    backdrop-filter: none;
    background-color: rgba(255, 255, 255, 0.9) !important;
  }
}

/* 🎯 动态性能优化类 */

/* 低性能设备优化 */
.low-performance-device .floating-icon {
  animation-duration: 10s !important;
  animation-timing-function: linear !important;
}

.low-performance-device .fade-in-section {
  transition-duration: 0.3s !important;
  transition-timing-function: ease !important;
}

/* 禁用复杂的视觉效果 */
.low-performance-device .bg-gradient-primary,
.low-performance-device .text-gradient {
  background: var(--primary) !important;
  background-clip: unset !important;
  -webkit-background-clip: unset !important;
  color: white !important;
}

/* 高性能设备增强 */
.high-performance-device .floating-icon {
  animation-duration: 3s !important;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.high-performance-device .floating-icon:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
}

/* 移动设备优化 */
.mobile-device .floating-icon {
  animation-duration: 8s !important;
}

/* 隐藏部分装饰元素以提升移动端性能 */
@media (max-width: 480px) {
  .mobile-device .floating-icon-4,
  .mobile-device .floating-icon-5 {
    display: none !important;
  }
}
</style>
