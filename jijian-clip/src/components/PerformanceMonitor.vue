<template>
  <div 
    v-if="showMonitor" 
    class="performance-monitor fixed top-4 right-4 z-50 bg-black/80 text-white text-xs p-3 rounded-lg backdrop-blur-sm font-mono"
    :class="{ 'performance-warning': fps < 30, 'performance-critical': fps < 20 }"
  >
    <div class="monitor-header flex items-center justify-between mb-2">
      <span class="font-semibold">🎯 性能监控</span>
      <button 
        @click="toggleMonitor" 
        class="text-white/60 hover:text-white transition-colors"
        title="关闭监控"
      >
        ✕
      </button>
    </div>
    
    <div class="monitor-stats space-y-1">
      <!-- FPS显示 -->
      <div class="flex justify-between">
        <span>FPS:</span>
        <span :class="getFPSClass(fps)">{{ fps }}</span>
      </div>
      
      <!-- 动画统计 -->
      <div class="flex justify-between">
        <span>运行动画:</span>
        <span>{{ animationStats.runningAnimations }}/{{ animationStats.maxConcurrentAnimations }}</span>
      </div>
      
      <div class="flex justify-between">
        <span>队列动画:</span>
        <span>{{ animationStats.pendingAnimations }}</span>
      </div>
      
      <!-- 内存使用 -->
      <div v-if="memoryInfo" class="flex justify-between">
        <span>内存:</span>
        <span>{{ formatMemory(memoryInfo.usedJSHeapSize) }}</span>
      </div>
      
      <!-- 设备信息 -->
      <div class="flex justify-between">
        <span>CPU核心:</span>
        <span>{{ deviceInfo.cores }}</span>
      </div>
      
      <!-- 网络状态 -->
      <div v-if="networkInfo" class="flex justify-between">
        <span>网络:</span>
        <span>{{ networkInfo.effectiveType }}</span>
      </div>
      
      <!-- 性能建议 -->
      <div v-if="performanceAdvice" class="mt-2 pt-2 border-t border-white/20">
        <div class="text-yellow-300 text-xs">
          💡 {{ performanceAdvice }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { getPerformanceStatus } from '@/utils/animationManager'

interface Props {
  enabled?: boolean
  autoHide?: boolean
  hideDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  enabled: true,
  autoHide: true,
  hideDelay: 10000 // 10秒后自动隐藏
})

// 响应式数据
const showMonitor = ref(false)
const fps = ref(60)
const animationStats = ref({
  runningAnimations: 0,
  pendingAnimations: 0,
  maxConcurrentAnimations: 8
})
const memoryInfo = ref<any>(null)
const deviceInfo = ref({
  cores: navigator.hardwareConcurrency || 4,
  memory: (navigator as any).deviceMemory || 'unknown'
})
const networkInfo = ref<any>(null)

// 性能监控状态
let isMonitoring = false
let frameCount = 0
let lastTime = performance.now()
let autoHideTimer: number | null = null

// 计算属性
const performanceAdvice = computed(() => {
  if (fps.value < 20) {
    return '性能严重不足，建议减少动画效果'
  } else if (fps.value < 30) {
    return '性能偏低，建议关闭部分动画'
  } else if (animationStats.value.runningAnimations > 6) {
    return '同时运行动画过多，可能影响性能'
  } else if (memoryInfo.value && memoryInfo.value.usedJSHeapSize > 100 * 1024 * 1024) {
    return '内存使用较高，注意内存泄漏'
  }
  return null
})

// 方法
const getFPSClass = (currentFPS: number) => {
  if (currentFPS >= 50) return 'text-green-400'
  if (currentFPS >= 30) return 'text-yellow-400'
  return 'text-red-400'
}

const formatMemory = (bytes: number) => {
  const mb = bytes / (1024 * 1024)
  return `${mb.toFixed(1)}MB`
}

const toggleMonitor = () => {
  showMonitor.value = !showMonitor.value
  
  if (showMonitor.value) {
    startMonitoring()
    resetAutoHideTimer()
  } else {
    stopMonitoring()
  }
}

const startMonitoring = () => {
  if (isMonitoring) return
  
  isMonitoring = true
  frameCount = 0
  lastTime = performance.now()
  
  const monitorFrame = (currentTime: number) => {
    frameCount++
    
    const deltaTime = currentTime - lastTime
    if (deltaTime >= 1000) {
      // 计算FPS
      fps.value = Math.round((frameCount * 1000) / deltaTime)
      frameCount = 0
      lastTime = currentTime
      
      // 更新动画统计
      animationStats.value = getPerformanceStatus()
      
      // 更新内存信息
      updateMemoryInfo()
      
      // 更新网络信息
      updateNetworkInfo()
    }
    
    if (isMonitoring) {
      requestAnimationFrame(monitorFrame)
    }
  }
  
  requestAnimationFrame(monitorFrame)
}

const stopMonitoring = () => {
  isMonitoring = false
  clearAutoHideTimer()
}

const updateMemoryInfo = () => {
  if ('memory' in performance) {
    memoryInfo.value = (performance as any).memory
  }
}

const updateNetworkInfo = () => {
  const connection = (navigator as any).connection
  if (connection) {
    networkInfo.value = {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt
    }
  }
}

const resetAutoHideTimer = () => {
  clearAutoHideTimer()
  
  if (props.autoHide) {
    autoHideTimer = window.setTimeout(() => {
      showMonitor.value = false
      stopMonitoring()
    }, props.hideDelay)
  }
}

const clearAutoHideTimer = () => {
  if (autoHideTimer) {
    clearTimeout(autoHideTimer)
    autoHideTimer = null
  }
}

// 键盘快捷键
const handleKeyPress = (event: KeyboardEvent) => {
  // Ctrl + Shift + P 切换性能监控
  if (event.ctrlKey && event.shiftKey && event.key === 'P') {
    event.preventDefault()
    toggleMonitor()
  }
}

// 生命周期
onMounted(() => {
  if (props.enabled) {
    // 开发环境自动显示
    if (import.meta.env.DEV) {
      showMonitor.value = true
      startMonitoring()
      resetAutoHideTimer()
    }
    
    // 添加键盘监听
    document.addEventListener('keydown', handleKeyPress)
    
    // 初始化设备信息
    updateMemoryInfo()
    updateNetworkInfo()
  }
})

onUnmounted(() => {
  stopMonitoring()
  document.removeEventListener('keydown', handleKeyPress)
})

// 暴露方法给父组件
defineExpose({
  show: () => {
    showMonitor.value = true
    startMonitoring()
    resetAutoHideTimer()
  },
  hide: () => {
    showMonitor.value = false
    stopMonitoring()
  },
  toggle: toggleMonitor
})
</script>

<style scoped>
.performance-monitor {
  min-width: 200px;
  user-select: none;
  transition: all 0.3s ease;
}

.performance-warning {
  border: 1px solid #f59e0b;
  box-shadow: 0 0 10px rgba(245, 158, 11, 0.3);
}

.performance-critical {
  border: 1px solid #ef4444;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.monitor-stats {
  font-size: 10px;
  line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .performance-monitor {
    top: 2px;
    right: 2px;
    font-size: 10px;
    padding: 8px;
    min-width: 160px;
  }
}
</style>
