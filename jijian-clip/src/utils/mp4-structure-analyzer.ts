/**
 * MP4文件结构分析工具
 * 用于诊断MP4文件的结构问题，特别是track数据解析问题
 */

export interface MP4StructureInfo {
  fileSize: number;
  hasValidStructure: boolean;
  boxes: string[];
  tracks: MP4TrackInfo[];
  issues: string[];
  recommendations: string[];
}

export interface MP4TrackInfo {
  id: number;
  type: 'video' | 'audio' | 'unknown';
  codec: string;
  duration: number;
  timescale: number;
  sampleCount: number;
  hasStsc: boolean;
  hasStco: boolean;
  hasCo64: boolean;
  hasSamples: boolean;
  issues: string[];
}

/**
 * 分析MP4文件结构
 */
export async function analyzeMP4Structure(file: File): Promise<MP4StructureInfo> {
  const result: MP4StructureInfo = {
    fileSize: file.size,
    hasValidStructure: false,
    boxes: [],
    tracks: [],
    issues: [],
    recommendations: []
  };

  try {
    // 动态加载MP4Box
    const MP4Box = await loadMP4Box();
    const arrayBuffer = await file.arrayBuffer();
    const mp4boxFile = MP4Box.createFile();

    // 收集box信息
    const foundBoxes: string[] = [];
    
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeout = setTimeout(() => {
        result.issues.push('MP4文件解析超时');
        result.recommendations.push('文件可能损坏或格式不标准');
        resolve(result);
      }, 10000);

      mp4boxFile.onReady = (info: any) => {
        clearTimeout(timeout);
        
        try {
          console.log('🔍 MP4文件信息:', info);
          
          // 分析基本结构
          result.hasValidStructure = !!(info.videoTracks || info.audioTracks);
          
          // 分析视频轨道
          if (info.videoTracks) {
            for (const track of info.videoTracks) {
              const trackInfo = analyzeTrack(track, 'video', mp4boxFile);
              result.tracks.push(trackInfo);
            }
          }
          
          // 分析音频轨道
          if (info.audioTracks) {
            for (const track of info.audioTracks) {
              const trackInfo = analyzeTrack(track, 'audio', mp4boxFile);
              result.tracks.push(trackInfo);
            }
          }
          
          // 收集问题和建议
          analyzeIssues(result);
          
          resolve(result);
        } catch (error) {
          result.issues.push(`解析错误: ${error.message}`);
          resolve(result);
        }
      };

      mp4boxFile.onError = (error: any) => {
        clearTimeout(timeout);
        result.issues.push(`MP4Box错误: ${error}`);
        result.recommendations.push('文件可能损坏，建议重新编码');
        resolve(result);
      };

      // 开始解析
      try {
        (arrayBuffer as any).fileStart = 0;
        mp4boxFile.appendBuffer(arrayBuffer);
        mp4boxFile.flush();
      } catch (error) {
        clearTimeout(timeout);
        result.issues.push(`文件读取错误: ${error.message}`);
        resolve(result);
      }
    });

  } catch (error) {
    result.issues.push(`初始化错误: ${error.message}`);
    return result;
  }
}

/**
 * 分析单个track
 */
function analyzeTrack(track: any, type: 'video' | 'audio', mp4boxFile: any): MP4TrackInfo {
  const trackInfo: MP4TrackInfo = {
    id: track.id,
    type,
    codec: track.codec || 'unknown',
    duration: track.duration || 0,
    timescale: track.timescale || 0,
    sampleCount: track.nb_samples || 0,
    hasStsc: false,
    hasStco: false,
    hasCo64: false,
    hasSamples: false,
    issues: []
  };

  try {
    // 检查track的基本属性
    if (!track.codec) {
      trackInfo.issues.push('缺少编码格式信息');
    }
    
    if (!track.timescale || track.timescale <= 0) {
      trackInfo.issues.push('时间刻度无效');
    }
    
    if (!track.nb_samples || track.nb_samples <= 0) {
      trackInfo.issues.push('样本数量无效');
    }

    // 检查samples
    if (track.samples && track.samples.length > 0) {
      trackInfo.hasSamples = true;
    } else {
      trackInfo.issues.push('samples数据缺失');
      
      // 尝试从完整track获取
      try {
        const fullTrack = mp4boxFile.getTrackById(track.id);
        if (fullTrack && fullTrack.samples && fullTrack.samples.length > 0) {
          trackInfo.hasSamples = true;
          trackInfo.issues.push('samples需要从完整track获取');
        }
      } catch (error) {
        trackInfo.issues.push('无法获取完整track信息');
      }
    }

    // 检查索引表
    trackInfo.hasStsc = !!(track.stsc && track.stsc.entries && track.stsc.entries.length > 0);
    trackInfo.hasStco = !!(track.stco && track.stco.entries && track.stco.entries.length > 0);
    trackInfo.hasCo64 = !!(track.co64 && track.co64.entries && track.co64.entries.length > 0);

    if (!trackInfo.hasStsc) {
      trackInfo.issues.push('缺少sample-to-chunk表(stsc)');
    }
    
    if (!trackInfo.hasStco && !trackInfo.hasCo64) {
      trackInfo.issues.push('缺少chunk偏移表(stco/co64)');
    }

  } catch (error) {
    trackInfo.issues.push(`track分析错误: ${error.message}`);
  }

  return trackInfo;
}

/**
 * 分析整体问题并生成建议
 */
function analyzeIssues(result: MP4StructureInfo): void {
  const videoTracks = result.tracks.filter(t => t.type === 'video');
  const audioTracks = result.tracks.filter(t => t.type === 'audio');

  // 检查是否有视频轨道
  if (videoTracks.length === 0) {
    result.issues.push('未找到视频轨道');
    result.recommendations.push('确认文件是否为有效的视频文件');
  }

  // 检查视频轨道问题
  for (const track of videoTracks) {
    if (track.issues.length > 0) {
      result.issues.push(`视频轨道${track.id}存在问题: ${track.issues.join(', ')}`);
      
      if (track.issues.some(issue => issue.includes('samples'))) {
        result.recommendations.push('MP4文件的索引结构可能不完整，建议重新编码');
      }
      
      if (track.issues.some(issue => issue.includes('stsc') || issue.includes('stco'))) {
        result.recommendations.push('缺少必要的索引表，建议使用FFmpeg重新封装: ffmpeg -i input.mp4 -c copy -movflags +faststart output.mp4');
      }
    }
  }

  // 生成通用建议
  if (result.issues.length > 0) {
    result.recommendations.push('如果问题持续存在，建议使用以下命令重新编码:');
    result.recommendations.push('ffmpeg -i input.mp4 -c:v libx264 -c:a aac -movflags +faststart output.mp4');
  }
}

/**
 * 动态加载MP4Box
 */
async function loadMP4Box(): Promise<any> {
  // 这里复用decoder.ts中的loadMP4Box逻辑
  if ((globalThis as any).MP4Box) {
    return (globalThis as any).MP4Box;
  }

  try {
    const mp4boxModule = await import('mp4box');
    return mp4boxModule.default || mp4boxModule.MP4Box || mp4boxModule;
  } catch (error) {
    throw new Error(`MP4Box加载失败: ${error.message}`);
  }
}

/**
 * 显示MP4结构分析报告
 */
export function displayMP4StructureReport(result: MP4StructureInfo): void {
  console.group('🎬 MP4文件结构分析报告');
  
  console.log('📊 基本信息:');
  console.log(`  - 文件大小: ${(result.fileSize / 1024 / 1024).toFixed(2)} MB`);
  console.log(`  - 结构有效: ${result.hasValidStructure ? '是' : '否'}`);
  console.log(`  - 轨道数量: ${result.tracks.length}`);
  
  if (result.tracks.length > 0) {
    console.log('🎯 轨道详情:');
    result.tracks.forEach(track => {
      console.log(`  轨道${track.id} (${track.type}):`);
      console.log(`    - 编码: ${track.codec}`);
      console.log(`    - 样本数: ${track.sampleCount}`);
      console.log(`    - 时长: ${(track.duration / track.timescale).toFixed(2)}s`);
      console.log(`    - 索引表: stsc=${track.hasStsc}, stco=${track.hasStco}, co64=${track.hasCo64}`);
      console.log(`    - 样本数据: ${track.hasSamples ? '有' : '无'}`);
      
      if (track.issues.length > 0) {
        console.log(`    - 问题: ${track.issues.join(', ')}`);
      }
    });
  }
  
  if (result.issues.length > 0) {
    console.log('⚠️ 发现的问题:');
    result.issues.forEach(issue => console.log(`  - ${issue}`));
  }
  
  if (result.recommendations.length > 0) {
    console.log('💡 建议:');
    result.recommendations.forEach(rec => console.log(`  - ${rec}`));
  }
  
  console.groupEnd();
}
