// 元数据构建器 - 创建符合后端接口的分段元数据
// 参考后端 api/types/segments_api.go 中的 SegmentMetadataRequest

import { ASRResult, ASRSentence } from './audio-processor.js';
import { VideoInfo } from './types.js';

// 视频基础信息（匹配后端 VideoInfo 结构）
export interface VideoMetaInfo {
  original_name: string; // 原始文件名
  client_path: string; // 客户端路径
  size_bytes: number; // 文件大小（字节）
}

// 音频句子（匹配后端 AudioSentence 结构）
export interface AudioSentence {
  start_time: number;
  end_time: number;
  content: string;
}

// 分段元数据（匹配后端 SegmentMetadata 结构）
export interface SegmentMetadata {
  segment_id: string; // 分段ID
  segment_index: number; // 分段索引
  start_time: number; // 开始时间
  end_time: number; // 结束时间
  duration: number; // 时长
  frame_count: number; // 帧数
  audio_sentences?: AudioSentence[]; // 音频句子
  has_audio: boolean; // 是否有音频
}

// 分段元数据请求（匹配后端 SegmentMetadataRequest 结构）
export interface SegmentMetadataRequest {
  video_code: string; // 视频代号
  video_info: VideoMetaInfo; // 视频基础信息
  video_duration: number; // 视频总时长
  total_segments: number; // 总分段数
  segments: SegmentMetadata[]; // 分段数组
}

// 元数据上传响应（匹配后端 MetadataResponse 结构）
export interface MetadataResponse {
  success: boolean;
  task_id: string;
  video_code: string;
  total_segments: number;
  message: string;
  error?: string;
  error_code?: number;
}

export interface MetadataBuilderConfig {
  targetFps: number; // 目标帧率，用于计算帧数
  minFrameCount: number; // 最小帧数限制
  maxFrameCount: number; // 最大帧数限制
  videoIndex?: number; // 视频索引，用于生成videoCode（默认为0，生成'a'）
  apiBaseUrl?: string; // API基础URL，默认为 https://localhost:60850/api/v1
}

export class MetadataBuilder {
  private config: MetadataBuilderConfig;

  constructor(config?: Partial<MetadataBuilderConfig>) {
    this.config = {
      targetFps: config?.targetFps ?? 5.0, // 默认5fps
      minFrameCount: config?.minFrameCount ?? 4, // 默认最小帧数4
      maxFrameCount: config?.maxFrameCount ?? 512, // 默认最大帧数512
      videoIndex: config?.videoIndex ?? 0, // 默认索引0，生成'a'
      apiBaseUrl: config?.apiBaseUrl ?? 'https://localhost:60850/api/v1', // 默认API地址
    };
  }

  /**
   * 构建分段元数据请求
   * @param boundaries 分段边界点
   * @param videoInfo 视频信息
   * @param videoFile 视频文件
   * @param asrResult ASR识别结果
   * @param videoCode 视频代号（可选，会自动生成）
   * @returns 分段元数据请求
   */
  public buildSegmentMetadata(
    boundaries: number[],
    videoInfo: VideoInfo,
    videoFile: File,
    asrResult?: ASRResult,
    videoCode?: string
  ): SegmentMetadataRequest {
    console.log('🏗️ 开始构建分段元数据...');

    // 生成或使用提供的视频代号
    const finalVideoCode = videoCode || this.generateVideoCode(videoFile.name);

    // 构建视频基础信息
    const videoMetaInfo: VideoMetaInfo = {
      original_name: videoFile.name,
      client_path: videoFile.name, // 前端使用文件名作为客户端路径
      size_bytes: videoFile.size
    };

    // 构建分段数组
    const segments = this.buildSegments(boundaries, finalVideoCode, asrResult);

    const metadata: SegmentMetadataRequest = {
      video_code: finalVideoCode,
      video_info: videoMetaInfo,
      video_duration: videoInfo.duration,
      total_segments: segments.length,
      segments: segments
    };

    console.log('✅ 分段元数据构建完成:', {
      video_code: finalVideoCode,
      video_duration: videoInfo.duration,
      total_segments: segments.length,
      hasAudio: (asrResult?.sentences?.length ?? 0) > 0
    });

    return metadata;
  }

  /**
   * 构建分段数组
   * @param boundaries 分段边界点
   * @param videoCode 视频代号
   * @param asrResult ASR识别结果
   * @returns 分段元数据数组
   */
  private buildSegments(
    boundaries: number[],
    videoCode: string,
    asrResult?: ASRResult
  ): SegmentMetadata[] {
    if (boundaries.length < 2) {
      console.warn('⚠️ 边界点数量不足，无法创建分段');
      return [];
    }

    const segments: SegmentMetadata[] = [];
    const sentences = asrResult?.sentences || [];

    for (let i = 0; i < boundaries.length - 1; i++) {
      const startTime = boundaries[i];
      const endTime = boundaries[i + 1];
      // 修复浮点数精度问题：四舍五入到3位小数
      const duration = Math.round((endTime - startTime) * 1000) / 1000;

      // 找到该分段内的音频句子
      const segmentSentences = this.findSentencesInSegment(startTime, endTime, sentences);

      // 计算帧数并应用最小/最大限制
      const originalFrameCount = Math.round(duration * this.config.targetFps);
      const frameCount = Math.max(this.config.minFrameCount, Math.min(this.config.maxFrameCount, originalFrameCount));

      // 如果帧数被限制，记录日志
      if (frameCount !== originalFrameCount) {
        console.log(`📊 分段 ${i + 1} 帧数限制: ${originalFrameCount} → ${frameCount} (范围: ${this.config.minFrameCount}-${this.config.maxFrameCount})`);
      }

      // 创建分段元数据（匹配后端格式：seg_a1, seg_a2, seg_b1, etc.）
      const segment: SegmentMetadata = {
        segment_id: `seg_${videoCode}${i + 1}`, // 格式：seg_a1, seg_a2, etc.
        segment_index: i,
        start_time: startTime,
        end_time: endTime,
        duration: duration,
        frame_count: frameCount,
        audio_sentences: segmentSentences.map(s => ({
          start_time: s.startTime,
          end_time: s.endTime,
          content: s.content
        })),
        has_audio: segmentSentences.length > 0
      };

      segments.push(segment);

      console.log(`📝 分段 ${i + 1}: ${startTime.toFixed(2)}s-${endTime.toFixed(2)}s, ${frameCount}帧, ${segmentSentences.length}句话`);
    }

    return segments;
  }

  /**
   * 找到分段内的音频句子
   * @param startTime 分段开始时间
   * @param endTime 分段结束时间
   * @param sentences 所有音频句子
   * @returns 分段内的句子
   */
  private findSentencesInSegment(
    startTime: number,
    endTime: number,
    sentences: ASRSentence[]
  ): ASRSentence[] {
    const segmentSentences: ASRSentence[] = [];

    for (const sentence of sentences) {
      // 句子与分段有重叠
      if (sentence.endTime > startTime && sentence.startTime < endTime) {
        segmentSentences.push(sentence);
      }
    }

    return segmentSentences;
  }

  /**
   * 生成视频代号（匹配后端格式：a, b, c, ..., z, aa, bb, cc, ...）
   * @param _fileName 文件名（未使用，保持接口兼容性）
   * @returns 视频代号
   */
  private generateVideoCode(_fileName: string): string {
    const index = this.config.videoIndex || 0;

    if (index < 26) {
      // 单字母：a, b, c, ..., z
      return String.fromCharCode('a'.charCodeAt(0) + index);
    }

    // 双字母：aa, bb, cc, ...
    const first = Math.floor(index / 26);
    const second = index % 26;
    return String.fromCharCode('a'.charCodeAt(0) + first - 1) + String.fromCharCode('a'.charCodeAt(0) + second);
  }

  /**
   * 验证元数据请求
   * @param metadata 元数据请求
   * @returns 验证结果
   */
  public validateMetadata(metadata: SegmentMetadataRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!metadata.video_code) {
      errors.push('视频代号不能为空');
    }

    if (!metadata.video_info.original_name) {
      errors.push('原始文件名不能为空');
    }

    if (metadata.video_duration <= 0) {
      errors.push('视频时长必须大于0');
    }

    if (metadata.total_segments <= 0) {
      errors.push('总分段数必须大于0');
    }

    if (metadata.segments.length !== metadata.total_segments) {
      errors.push('分段数量与总分段数不匹配');
    }

    // 验证每个分段
    for (let i = 0; i < metadata.segments.length; i++) {
      const segment = metadata.segments[i];
      
      if (!segment.segment_id) {
        errors.push(`分段${i + 1}的ID不能为空`);
      }

      if (segment.start_time < 0 || segment.end_time <= segment.start_time) {
        errors.push(`分段${i + 1}的时间范围无效`);
      }

      if (segment.duration <= 0) {
        errors.push(`分段${i + 1}的时长必须大于0`);
      }

      if (segment.frame_count < 0) {
        errors.push(`分段${i + 1}的帧数不能为负数`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 上传分段元数据到后端
   * @param metadata 分段元数据请求
   * @param taskId 任务ID
   * @returns 上传响应
   */
  public async uploadMetadata(metadata: SegmentMetadataRequest, taskId: string): Promise<MetadataResponse> {
    console.log('📤 开始上传分段元数据...', {
      taskId,
      video_code: metadata.video_code,
      total_segments: metadata.total_segments
    });

    // 验证元数据
    const validation = this.validateMetadata(metadata);
    if (!validation.valid) {
      throw new Error(`元数据验证失败: ${validation.errors.join(', ')}`);
    }

    // 验证任务ID
    if (!taskId || taskId.trim() === '') {
      throw new Error('任务ID不能为空');
    }

    const url = `${this.config.apiBaseUrl}/segments/upload-metadata`;

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Task-ID': taskId, // 后端要求在Header中传递任务ID
        },
        body: JSON.stringify(metadata),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const apiResponse = await response.json();

      // 检查API响应格式
      if (apiResponse.code !== 200 || !apiResponse.data) {
        throw new Error(apiResponse.message || '上传失败，API响应格式错误');
      }

      const result: MetadataResponse = apiResponse.data;

      if (!result.success) {
        throw new Error(result.error || '上传失败，未知错误');
      }

      console.log('✅ 元数据上传成功:', {
        taskId: result.task_id,
        videoCode: result.video_code,
        totalSegments: result.total_segments,
        message: result.message
      });

      return result;

    } catch (error) {
      console.error('❌ 元数据上传失败:', error);

      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new Error(`网络连接失败，请检查后端服务是否运行在 ${this.config.apiBaseUrl}`);
      }

      throw error;
    }
  }

  /**
   * 获取默认配置
   */
  public static getDefaultConfig(): MetadataBuilderConfig {
    return {
      targetFps: 5.0,
      minFrameCount: 4, // 默认最小帧数4
      maxFrameCount: 512, // 默认最大帧数512
      videoIndex: 0, // 默认生成'a'
      apiBaseUrl: 'https://localhost:60850/api/v1'
    };
  }
}
