// 音频处理器 - 从视频中提取音频并调用ASR API

export interface ASRConfig {
  language: string;
  enablePunc: boolean;
  enableWords: boolean;
  model: string;
}

export interface ASRSentence {
  startTime: number;
  endTime: number;
  content: string;
}

export interface ASRResult {
  success: boolean;
  processingId: string;
  fileName: string;
  duration: number;
  sentences: ASRSentence[];
  processingTime: string;
}

export interface AudioProcessingCallbacks {
  onProgress: (status: string) => void;
  onComplete: (result: ASRResult) => void;
  onError: (error: Error) => void;
}

export class AudioProcessor {
  private taskId: string;
  private backendUrl: string = 'https://localhost:49306';
  private callbacks: AudioProcessingCallbacks;

  constructor(taskId: string, callbacks: AudioProcessingCallbacks) {
    this.taskId = taskId;
    this.callbacks = callbacks;
  }

  // 主要处理方法：从视频文件提取音频并进行ASR处理
  async processVideoAudio(videoFile: File, asrConfig?: ASRConfig): Promise<void> {
    try {
      this.callbacks.onProgress('🎵 开始从视频中提取音频...');
      
      // 1. 从视频文件提取音频
      const audioBuffer = await this.extractAudioFromVideo(videoFile);
      
      this.callbacks.onProgress('🔄 转换音频格式...');
      
      // 2. 转换为WAV格式
      const wavBlob = this.audioBufferToWav(audioBuffer);
      this.callbacks.onProgress('📡 发送到ASR服务器...');

      // 3. 调用后端ASR API
      const result = await this.callASRAPI(wavBlob, videoFile.name, asrConfig);
      
      this.callbacks.onComplete(result);
      
    } catch (error) {
      console.error('音频处理失败:', error);
      this.callbacks.onError(error as Error);
    }
  }

  // 从视频文件中提取音频（匹配后端ffmpeg参数）
  private async extractAudioFromVideo(videoFile: File): Promise<AudioBuffer> {
    return new Promise((resolve, reject) => {
      // 创建音频上下文，目标采样率16kHz（匹配后端）
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 16000
      });

      const fileReader = new FileReader();
      fileReader.onload = async (e) => {
        try {
          const arrayBuffer = e.target?.result as ArrayBuffer;
          let audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

          // 如果不是16kHz，需要重采样
          if (audioBuffer.sampleRate !== 16000) {
            console.log(`🔄 重采样音频: ${audioBuffer.sampleRate}Hz -> 16000Hz`);
            audioBuffer = await this.resampleAudio(audioBuffer, 16000);
          }

          // 如果是立体声，转换为单声道（匹配后端 ac:1）
          if (audioBuffer.numberOfChannels > 1) {
            console.log(`🔄 转换为单声道: ${audioBuffer.numberOfChannels} -> 1 channels`);
            audioBuffer = this.convertToMono(audioBuffer);
          }

          console.log(`✅ 音频提取完成: ${audioBuffer.sampleRate}Hz, ${audioBuffer.numberOfChannels}ch, ${audioBuffer.duration.toFixed(2)}s`);
          resolve(audioBuffer);
        } catch (error) {
          reject(new Error(`音频解码失败: ${error}`));
        }
      };

      fileReader.onerror = () => {
        reject(new Error('文件读取失败'));
      };

      fileReader.readAsArrayBuffer(videoFile);
    });
  }

  // 重采样音频到目标采样率
  private async resampleAudio(audioBuffer: AudioBuffer, targetSampleRate: number): Promise<AudioBuffer> {
    // 创建离线音频上下文进行重采样
    const offlineContext = new OfflineAudioContext(
      audioBuffer.numberOfChannels,
      Math.ceil(audioBuffer.duration * targetSampleRate),
      targetSampleRate
    );

    const source = offlineContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(offlineContext.destination);
    source.start();

    return await offlineContext.startRendering();
  }

  // 转换立体声为单声道
  private convertToMono(audioBuffer: AudioBuffer): AudioBuffer {
    if (audioBuffer.numberOfChannels === 1) {
      return audioBuffer;
    }

    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
      sampleRate: audioBuffer.sampleRate
    });

    const monoBuffer = audioContext.createBuffer(1, audioBuffer.length, audioBuffer.sampleRate);
    const monoData = monoBuffer.getChannelData(0);

    // 混合所有声道为单声道
    for (let i = 0; i < audioBuffer.length; i++) {
      let sum = 0;
      for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
        sum += audioBuffer.getChannelData(channel)[i];
      }
      monoData[i] = sum / audioBuffer.numberOfChannels;
    }

    return monoBuffer;
  }

  // 将AudioBuffer转换为WAV格式的Blob（匹配后端ffmpeg格式）
  private audioBufferToWav(audioBuffer: AudioBuffer): Blob {
    const numberOfChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const length = audioBuffer.length * numberOfChannels * 2; // 16-bit samples
    const buffer = new ArrayBuffer(44 + length);
    const view = new DataView(buffer);

    // 调试信息：验证音频参数匹配后端要求
    console.log(`📊 WAV格式参数:
      - 采样率: ${sampleRate}Hz (目标: 16000Hz)
      - 声道数: ${numberOfChannels} (目标: 1)
      - 位深度: 16-bit PCM (匹配后端)
      - 字节序: Little Endian (匹配后端)
      - 时长: ${audioBuffer.duration.toFixed(2)}秒
      - 文件大小: ${(44 + length) / 1024}KB`);

    if (sampleRate !== 16000) {
      console.warn(`⚠️ 采样率不匹配！当前: ${sampleRate}Hz, 期望: 16000Hz`);
    }
    if (numberOfChannels !== 1) {
      console.warn(`⚠️ 声道数不匹配！当前: ${numberOfChannels}, 期望: 1`);
    }
    
    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    // RIFF chunk descriptor
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length, true);
    writeString(8, 'WAVE');
    
    // FMT sub-chunk
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true); // Sub-chunk size
    view.setUint16(20, 1, true); // Audio format (PCM)
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true); // Byte rate
    view.setUint16(32, numberOfChannels * 2, true); // Block align
    view.setUint16(34, 16, true); // Bits per sample
    
    // Data sub-chunk
    writeString(36, 'data');
    view.setUint32(40, length, true);
    
    // Convert audio data
    let offset = 44;
    for (let i = 0; i < audioBuffer.length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = audioBuffer.getChannelData(channel)[i];
        const intSample = Math.max(-1, Math.min(1, sample)) * 0x7FFF;
        view.setInt16(offset, intSample, true);
        offset += 2;
      }
    }
    
    return new Blob([buffer], { type: 'audio/wav' });
  }

  // 调用后端ASR API
  private async callASRAPI(audioBlob: Blob, fileName: string, asrConfig?: ASRConfig): Promise<ASRResult> {
    const formData = new FormData();
    formData.append('audio_file', audioBlob, `${fileName}.wav`);

    // 添加ASR配置
    if (asrConfig) {
      formData.append('config', JSON.stringify(asrConfig));
    }

    const response = await fetch(`${this.backendUrl}/api/v1/asr/audio-recognition`, {
      method: 'POST',
      headers: {
        'X-Task-ID': this.taskId,
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`ASR API调用失败: ${response.status} ${errorText}`);
    }

    const result = await response.json();

    // 检查API响应格式
    if (result.code !== 200) {
      throw new Error(`ASR API调用失败: ${result.message || '未知错误'}`);
    }

    // 检查ASR处理结果
    if (!result.data || !result.data.success) {
      throw new Error(`ASR处理失败: ${result.data?.error || result.message || '未知错误'}`);
    }

    // 正确映射后端数据结构到前端期望的格式
    const backendData = result.data;
    const sentences: ASRSentence[] = [];

    // 从后端的result.result.sentences中提取句子数据
    if (backendData.result && backendData.result.sentences) {
      for (const sentence of backendData.result.sentences) {
        sentences.push({
          startTime: sentence.start_time,
          endTime: sentence.end_time,
          content: sentence.content
        });
      }
    }

    console.log(`✅ ASR识别完成: 识别到 ${sentences.length} 个句子`);
    sentences.forEach((sentence, index) => {
      console.log(`句子 ${index + 1}: [${sentence.startTime.toFixed(2)}s - ${sentence.endTime.toFixed(2)}s] ${sentence.content}`);
    });

    // 返回前端期望的数据格式
    return {
      success: backendData.success,
      processingId: backendData.processing_id,
      fileName: backendData.file_name,
      duration: backendData.duration,
      sentences: sentences,
      processingTime: backendData.processing_time
    };
  }

  // 获取默认ASR配置
  static getDefaultASRConfig(): ASRConfig {
    return {
      language: 'zh-cn',
      enablePunc: true,
      enableWords: true,
      model: 'default'
    };
  }


}
