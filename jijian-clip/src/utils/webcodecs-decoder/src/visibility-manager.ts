/**
 * 页面可见性管理器
 * 解决macOS App Nap和浏览器后台优化导致的VideoFrame泄漏问题
 */
export class VisibilityManager {
  private isVisible: boolean = true;
  private listeners: Array<{
    onHidden: () => void;
    onVisible: () => void;
  }> = [];

  constructor() {
    this.setupVisibilityListener();
  }

  private setupVisibilityListener(): void {
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', () => {
      const wasVisible = this.isVisible;
      this.isVisible = !document.hidden;

      console.log(`📱 页面可见性变化: ${this.isVisible ? '可见' : '隐藏'}`);

      if (wasVisible && !this.isVisible) {
        // 页面变为隐藏
        this.notifyHidden();
      } else if (!wasVisible && this.isVisible) {
        // 页面变为可见
        this.notifyVisible();
      }
    });

    // 监听窗口焦点变化（额外保护）
    window.addEventListener('blur', () => {
      if (this.isVisible) {
        console.log('🔍 窗口失去焦点');
        // 不立即暂停，因为可能只是临时失焦
      }
    });

    window.addEventListener('focus', () => {
      if (this.isVisible) {
        console.log('🔍 窗口获得焦点');
      }
    });

    // 监听页面卸载（确保清理）
    window.addEventListener('beforeunload', () => {
      console.log('🚪 页面即将卸载，清理所有资源');
      this.notifyHidden();
    });
  }

  /**
   * 注册可见性变化监听器
   */
  addListener(listener: { onHidden: () => void; onVisible: () => void }): void {
    this.listeners.push(listener);
  }

  /**
   * 移除监听器
   */
  removeListener(listener: { onHidden: () => void; onVisible: () => void }): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * 获取当前可见性状态
   */
  getIsVisible(): boolean {
    return this.isVisible;
  }

  private notifyHidden(): void {
    console.log('⏸️ 通知所有监听器：页面隐藏');
    this.listeners.forEach(listener => {
      try {
        listener.onHidden();
      } catch (error) {
        console.error('❌ 页面隐藏监听器错误:', error);
      }
    });
  }

  private notifyVisible(): void {
    console.log('▶️ 通知所有监听器：页面可见');
    this.listeners.forEach(listener => {
      try {
        listener.onVisible();
      } catch (error) {
        console.error('❌ 页面可见监听器错误:', error);
      }
    });
  }

  /**
   * 手动触发暂停（用于测试）
   */
  forcePause(): void {
    if (this.isVisible) {
      console.log('🔧 手动触发暂停');
      this.notifyHidden();
    }
  }

  /**
   * 手动触发恢复（用于测试）
   */
  forceResume(): void {
    if (this.isVisible) {
      console.log('🔧 手动触发恢复');
      this.notifyVisible();
    }
  }
}

// 全局单例
export const visibilityManager = new VisibilityManager();
