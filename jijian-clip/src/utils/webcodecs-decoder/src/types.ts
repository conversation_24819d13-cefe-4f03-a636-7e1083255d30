// 简化的视频帧接口 - 直接使用WebCodecs VideoFrame
export interface YUVFrame {
  width: number;
  height: number;
  timestamp: number;
  format: string; // VideoFrame的原生格式 (NV12, I420等)
  index?: number;
  videoFrame: VideoFrame; // 直接使用VideoFrame对象
}

// 解码器配置
export interface DecoderConfig {
  codec: string;
  outputFormat: 'I420' | 'NV12';
  hardwareAcceleration?: 'prefer-hardware' | 'prefer-software' | 'no-preference';
}

// 解码器事件回调
export interface DecoderCallbacks {
  onFrame: (frame: YUVFrame) => void;
  onError: (error: Error) => void;
  onComplete: () => void;
  onProgress?: (progress: number) => void;
}

// 视频信息
export interface VideoInfo {
  width: number;
  height: number;
  duration: number;
  frameRate: number;
  codec: string;
  totalFrames?: number;
}

// 解码器状态
export type DecoderState = 'idle' | 'configuring' | 'decoding' | 'completed' | 'error';

// 帧差异特征
export interface FrameDifference {
  colorDiff: number;
  motionDiff: number;
  textureDiff: number;
  frameIndex: number;
  timestamp: number;
}
