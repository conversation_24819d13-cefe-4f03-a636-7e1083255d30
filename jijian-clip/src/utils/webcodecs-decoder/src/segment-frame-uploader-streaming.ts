// 真正的流式上传器 - 先发送请求头，然后逐个发送片段数据
// 核心原则：一个HTTP请求，读取完一个片段立即发送一个片段，异步进行

import { SegmentMetadataRequest } from './metadata-builder.js';

// 上传配置
export interface UploadConfig {
  apiBaseUrl: string;
  taskId: string;
  videoCode: string;
  jpegQuality: number; // 1-100，数值越大质量越高（与Canvas标准一致）
}

// 上传结果
export interface UploadResult {
  success: boolean;
  processedCount: number;
  failedCount: number;
  message: string;
}

/**
 * 真正的流式分段帧上传器
 * 使用浏览器原生API（HTMLVideoElement + Canvas）提取JPG帧
 * 先发送HTTP请求头，然后逐个发送片段数据
 */
export class StreamingSegmentFrameUploader {
  private config: UploadConfig;
  private videoElement: HTMLVideoElement | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private canvasContext: CanvasRenderingContext2D | null = null;
  private abortController: AbortController | null = null;

  constructor(config: UploadConfig) {
    this.config = config;
    console.log('🔧 流式分段帧上传器已创建');
  }

  /**
   * 初始化视频元素和Canvas
   */
  private async initializeVideoElement(videoFile: File): Promise<void> {
    console.log('🔧 初始化视频元素...');

    // 创建隐藏的video元素
    this.videoElement = document.createElement('video');
    this.videoElement.style.display = 'none';
    this.videoElement.muted = true; // 静音以避免自动播放限制
    this.videoElement.preload = 'metadata';
    document.body.appendChild(this.videoElement);

    // 创建canvas用于帧提取
    this.canvas = document.createElement('canvas');
    this.canvasContext = this.canvas.getContext('2d');

    if (!this.canvasContext) {
      throw new Error('无法创建Canvas 2D上下文');
    }

    // 加载视频文件
    return new Promise((resolve, reject) => {
      if (!this.videoElement) {
        reject(new Error('视频元素未创建'));
        return;
      }

      this.videoElement.onloadedmetadata = () => {
        if (!this.videoElement) return;

        console.log(`✅ 视频加载完成: ${this.videoElement.videoWidth}x${this.videoElement.videoHeight}, 时长: ${this.videoElement.duration}s`);

        // 设置canvas尺寸
        if (this.canvas) {
          this.canvas.width = this.videoElement.videoWidth;
          this.canvas.height = this.videoElement.videoHeight;
        }

        resolve();
      };

      this.videoElement.onerror = () => {
        reject(new Error('视频加载失败'));
      };

      // 设置视频源
      this.videoElement.src = URL.createObjectURL(videoFile);
    });
  }

  /**
   * 更新任务ID
   */
  updateTaskId(taskId: string): void {
    this.config.taskId = taskId;
    console.log('🔧 上传器任务ID已更新为:', taskId);
  }

  /**
   * 更新视频代码
   */
  updateVideoCode(videoCode: string): void {
    this.config.videoCode = videoCode;
    console.log('🔧 上传器视频代码已更新为:', videoCode);
  }

  /**
   * 开始真正的流式上传分段帧数据
   * 读一个片段立即发一个片段，不缓存数据
   */
  async uploadSegmentFrames(videoFile: File, segmentMetadata: SegmentMetadataRequest): Promise<UploadResult> {
    console.log('🚀 开始真正的流式上传 (读一个片段立即发一个片段)...', {
      totalSegments: segmentMetadata.segments.length,
      taskId: this.config.taskId,
      videoCode: this.config.videoCode
    });

    // 初始化视频元素
    await this.initializeVideoElement(videoFile);

    this.abortController = new AbortController();
    let processedCount = 0;
    let failedCount = 0;

    try {
      // 1. 建立HTTP连接，发送请求头
      console.log('🌐 建立HTTP连接，发送请求头...');
      const { sendData, closeStream, responsePromise } = await this.createStreamConnection();

      // 2. 启动异步读取和发送任务
      console.log('🏭 启动异步读取和发送任务...');
      const processingPromise = this.startStreamingProcess(videoFile, segmentMetadata, sendData, closeStream);

      // 3. 等待处理完成和响应
      const [processingResult, response] = await Promise.allSettled([
        processingPromise,
        responsePromise
      ]);

      // 4. 处理结果
      if (processingResult.status === 'fulfilled') {
        processedCount = processingResult.value.processedCount;
        failedCount = processingResult.value.failedCount;
      } else {
        console.error('❌ 流式处理失败:', processingResult.reason);
        failedCount = segmentMetadata.segments.length;
      }

      if (response.status === 'rejected') {
        console.error('❌ HTTP响应失败:', response.reason);
        throw response.reason;
      }

      const success = failedCount === 0;
      const message = success
        ? `成功流式上传 ${processedCount} 个分段`
        : `流式上传完成，${processedCount} 个成功，${failedCount} 个失败`;

      return { success, processedCount, failedCount, message };

    } catch (error) {
      console.error('❌ 流式上传失败:', error);
      return {
        success: false,
        processedCount,
        failedCount: segmentMetadata.segments.length - processedCount,
        message: `流式上传失败: ${error instanceof Error ? error.message : '未知错误'}`
      };
    } finally {
      this.abortController = null;
      this.cleanupVideoElement();
    }
  }

  /**
   * 建立HTTPS HTTP/2流式连接
   */
  private async createStreamConnection(): Promise<{
    sendData: (data: Uint8Array) => Promise<void>;
    closeStream: () => Promise<void>;
    responsePromise: Promise<void>;
  }> {
    console.log('🌐 建立HTTPS HTTP/2流式连接...');
    return await this.createHTTP2StreamConnection();
  }

  /**
   * 尝试HTTP/2流式连接
   */
  private async createHTTP2StreamConnection(): Promise<{
    sendData: (data: Uint8Array) => Promise<void>;
    closeStream: () => Promise<void>;
    responsePromise: Promise<void>;
  }> {
    const url = `${this.config.apiBaseUrl}/segments/stream-analysis`;

    let streamClosed = false;
    let streamController: ReadableStreamDefaultController<Uint8Array> | null = null;

    // 创建ReadableStream - 使用正确的流式实现
    const stream = new ReadableStream<Uint8Array>({
      start(controller) {
        console.log('📡 HTTP/2流控制器已准备就绪');
        streamController = controller;
        // 不在start中处理数据，而是通过外部控制
      },
      cancel(reason) {
        console.log('� HTTP/2流被取消:', reason);
        streamClosed = true;
        streamController = null;
      }
    });

    console.log('🔧 ReadableStream已创建，准备发送fetch请求...');

    // 发送流式请求 - 关键：添加duplex选项启用真正的流式上传
    const requestInit: any = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/octet-stream',
        'X-Task-ID': this.config.taskId,
        'X-Video-Code': this.config.videoCode,
      },
      body: stream,
      // 关键：启用流式上传的duplex选项
      duplex: 'half', // 启用HTTP/2流式上传
      signal: this.abortController?.signal
    };

    console.log('🚀 发送HTTP/2流式请求到:', url);
    console.log('📋 请求头:', requestInit.headers);
    console.log('🔧 duplex模式:', requestInit.duplex);

    const responsePromise = fetch(url, requestInit).then(async (response) => {
      console.log(`📡 收到HTTP响应: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      if (result.code !== 200 || !result.data) {
        throw new Error(result.message || '流式上传失败');
      }

      console.log('✅ HTTP/2流式请求成功完成');
    }).catch(error => {
      console.error('❌ HTTP/2请求失败:', error);
      throw error;
    });

    const sendData = async (data: Uint8Array): Promise<void> => {
      if (streamClosed || !streamController) {
        throw new Error('流已关闭或控制器不可用');
      }

      console.log(`📤 立即发送数据块，大小: ${data.length} 字节`);
      try {
        streamController.enqueue(data);
      } catch (error) {
        console.error('❌ 发送数据失败:', error);
        throw error;
      }
    };

    const closeStream = async (): Promise<void> => {
      if (streamController && !streamClosed) {
        console.log('🔒 关闭HTTP/2流');
        try {
          streamController.close();
        } catch (error) {
          console.error('❌ 关闭流失败:', error);
        }
        streamClosed = true;
        streamController = null;
      }
    };

    console.log('✅ HTTP/2连接已建立，启用duplex流式上传');
    return { sendData, closeStream, responsePromise };
  }



  /**
   * 启动流式处理：异步读取帧并发送
   */
  private async startStreamingProcess(
    _videoFile: File, // 参数保留但标记为未使用
    segmentMetadata: SegmentMetadataRequest,
    sendData: (data: Uint8Array) => Promise<void>,
    closeStream: () => Promise<void>
  ): Promise<{ processedCount: number; failedCount: number }> {
    let processedCount = 0;
    let failedCount = 0;

    try {
      // 逐个处理分段
      for (let i = 0; i < segmentMetadata.segments.length; i++) {
        // 检查是否被取消
        if (this.abortController?.signal.aborted) {
          console.log('🛑 流式处理被取消');
          break;
        }

        const segment = segmentMetadata.segments[i];
        console.log(`🎬 处理分段 ${i + 1}/${segmentMetadata.segments.length}: ${segment.segment_id}`);

        try {
          // 1. 提取分段帧
          const jpegFrames = await this.extractSegmentFrames(segment);

          // 2. 编码为二进制数据
          const binaryData = this.encodeSegmentToBinary(i, jpegFrames);

          // 3. 立即发送到流
          console.log(`📤 发送分段 ${i} 数据，大小: ${binaryData.length} 字节`);
          await sendData(binaryData);

          processedCount++;
          console.log(`✅ 分段 ${i} 已发送`);

          // 4. 立即释放内存
          jpegFrames.length = 0;

        } catch (error) {
          failedCount++;
          console.error(`❌ 分段 ${i} 处理失败:`, error);
        }
      }

      // 关闭流
      await closeStream();
      console.log('🏭 流式处理完成，数据流已关闭');

      return { processedCount, failedCount };

    } catch (error) {
      console.error('❌ 流式处理异常:', error);
      try {
        await closeStream();
      } catch (closeError) {
        console.error('❌ 流关闭失败:', closeError);
      }

      return { processedCount, failedCount: segmentMetadata.segments.length - processedCount };
    }
  }

  /**
   * 使用原生API提取单个分段的JPG帧
   */
  private async extractSegmentFrames(segment: any): Promise<Uint8Array[]> {
    console.log(`🎬 使用原生API提取JPG帧: ${segment.start_time}s - ${segment.end_time}s`);

    if (!this.videoElement || !this.canvas || !this.canvasContext) {
      throw new Error('视频元素或Canvas未初始化');
    }

    const segmentDuration = segment.end_time - segment.start_time;
    const requiredFrameCount = segment.frame_count; // 使用分段要求的固定帧数
    const timeInterval = segmentDuration / requiredFrameCount; // 计算时间间隔

    console.log(`📊 分段参数: 时长=${segmentDuration.toFixed(3)}s, 要求帧数=${requiredFrameCount}, 时间间隔=${timeInterval.toFixed(3)}s`);

    const jpegFrames: Uint8Array[] = [];

    try {
      // 逐帧提取
      for (let i = 0; i < requiredFrameCount; i++) {
        if (this.abortController?.signal.aborted) {
          throw new Error('提取被取消');
        }

        // 计算当前帧的时间点
        const targetTime = segment.start_time + (i * timeInterval);

        // 确保不超过分段结束时间
        const actualTime = Math.min(targetTime, segment.end_time - 0.001); // 减去1ms避免边界问题

        console.log(`📸 提取第 ${i + 1}/${requiredFrameCount} 帧，时间: ${actualTime.toFixed(3)}s`);

        // 提取该时间点的帧
        const frameData = await this.extractFrameAtTime(actualTime);
        jpegFrames.push(frameData);
      }

      console.log(`✅ 原生API提取完成，共 ${jpegFrames.length} 帧`);
      return jpegFrames;

    } catch (error) {
      console.error('❌ 原生API提取失败:', error);
      throw error;
    }
  }

  /**
   * 在指定时间点提取帧
   */
  private async extractFrameAtTime(targetTime: number): Promise<Uint8Array> {
    if (!this.videoElement || !this.canvas || !this.canvasContext) {
      throw new Error('视频元素或Canvas未初始化');
    }

    return new Promise((resolve, reject) => {
      const video = this.videoElement!;
      const canvas = this.canvas!;
      const ctx = this.canvasContext!;

      // 设置超时，避免无限等待
      const timeout = setTimeout(() => {
        reject(new Error(`提取帧超时: ${targetTime}s`));
      }, 5000);

      const onSeeked = () => {
        try {
          // 清除超时
          clearTimeout(timeout);

          // 绘制当前帧到canvas
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // 转换为JPEG blob
          canvas.toBlob((blob) => {
            if (!blob) {
              reject(new Error('Canvas转换为blob失败'));
              return;
            }

            // 转换为Uint8Array
            blob.arrayBuffer().then(buffer => {
              resolve(new Uint8Array(buffer));
            }).catch(reject);

          }, 'image/jpeg', this.config.jpegQuality / 100);

        } catch (error) {
          clearTimeout(timeout);
          reject(error);
        }
      };

      // 监听seeked事件
      video.addEventListener('seeked', onSeeked, { once: true });

      // 跳转到目标时间
      video.currentTime = targetTime;
    });
  }

  /**
   * 将分段编码为二进制流
   */
  private encodeSegmentToBinary(segmentIndex: number, jpegFrames: Uint8Array[]): Uint8Array {
    console.log(`🔧 编码分段 ${segmentIndex}，帧数: ${jpegFrames.length}`);

    // 计算总大小
    let totalSize = 8; // 分段索引(4) + 帧数量(4)
    for (const frame of jpegFrames) {
      totalSize += 4 + frame.length; // 帧长度(4) + 帧数据
    }

    const buffer = new ArrayBuffer(totalSize);
    const view = new DataView(buffer);
    let offset = 0;

    // 写入分段索引 (大端序)
    view.setUint32(offset, segmentIndex, false);
    offset += 4;

    // 写入帧数量 (大端序)
    view.setUint32(offset, jpegFrames.length, false);
    offset += 4;

    // 写入所有帧数据
    for (const frame of jpegFrames) {
      view.setUint32(offset, frame.length, false);
      offset += 4;

      new Uint8Array(buffer, offset, frame.length).set(frame);
      offset += frame.length;
    }

    console.log(`✅ 分段 ${segmentIndex} 编码完成，大小: ${totalSize} 字节`);
    return new Uint8Array(buffer);
  }

  /**
   * 清理视频元素和Canvas
   */
  private cleanupVideoElement(): void {
    if (this.videoElement) {
      // 清理video元素
      if (this.videoElement.src) {
        URL.revokeObjectURL(this.videoElement.src);
      }
      if (this.videoElement.parentNode) {
        this.videoElement.parentNode.removeChild(this.videoElement);
      }
      this.videoElement = null;
      console.log('🧹 视频元素已清理');
    }

    if (this.canvas) {
      this.canvas = null;
      this.canvasContext = null;
      console.log('🧹 Canvas已清理');
    }
  }

  /**
   * 取消上传
   */
  cancel(): void {
    this.abortController?.abort();
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.cancel();
    this.cleanupVideoElement();
    console.log('🧹 流式分段帧上传器资源已清理');
  }

  /**
   * 获取默认配置 - 使用HTTPS HTTP/2
   */
  static getDefaultConfig(taskId: string, videoCode: string): UploadConfig {
    return {
      apiBaseUrl: 'https://localhost:49306/api/v1', // 使用HTTPS HTTP/2
      taskId,
      videoCode,
      jpegQuality: 80 // Canvas JPEG质量参数 (1-100, 数值越大质量越高)
    };
  }
}
