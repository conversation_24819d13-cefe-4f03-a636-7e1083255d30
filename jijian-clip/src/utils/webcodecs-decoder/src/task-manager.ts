// 任务管理器 - 负责与后端的任务API交互

// 任务相关类型定义
export interface TaskCreateRequest {
  name: string;
  description?: string;
  config?: Record<string, any>;
}

export interface TaskResponse {
  task_id: string;
  status: string;
  created_at: string;
  expires_at: string;
}

export interface TaskStatusResponse {
  task_id: string;
  status: string;
  current_step: string;
  progress?: {
    percentage: number;
    current_stage: string;
    estimated_remaining: string;
  };
  started_at?: string;
  elapsed_time?: string;
  video_count: number;
  completed_videos: number;
  error?: string;
}

export interface APIResponse<T> {
  code: number;
  message: string;
  data?: T;
  error?: {
    type: string;
    details: string;
    field?: string;
    timestamp: string;
  };
  request_id: string;
}

// 任务管理器类
export class TaskManager {
  private baseUrl: string;

  constructor(baseUrl: string = 'https://localhost:49306') {
    this.baseUrl = baseUrl;
  }

  // 创建新任务
  async createTask(request: TaskCreateRequest): Promise<TaskResponse> {
    console.log('🆕 创建任务:', request);
    
    const response = await fetch(`${this.baseUrl}/api/v1/task/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`创建任务失败: ${response.status} ${errorText}`);
    }

    const result: APIResponse<TaskResponse> = await response.json();
    
    if (result.code !== 200 || !result.data) {
      throw new Error(`创建任务失败: ${result.message || '未知错误'}`);
    }

    console.log('✅ 任务创建成功:', result.data);
    return result.data;
  }

  // 获取任务状态
  async getTaskStatus(taskId: string): Promise<TaskStatusResponse> {
    console.log('📊 获取任务状态:', taskId);
    
    const response = await fetch(`${this.baseUrl}/api/v1/task/status`, {
      method: 'GET',
      headers: {
        'X-Task-ID': taskId,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`获取任务状态失败: ${response.status} ${errorText}`);
    }

    const result: APIResponse<TaskStatusResponse> = await response.json();
    
    if (result.code !== 200 || !result.data) {
      throw new Error(`获取任务状态失败: ${result.message || '未知错误'}`);
    }

    console.log('📊 任务状态:', result.data);
    return result.data;
  }

  // 删除任务
  async deleteTask(taskId: string): Promise<void> {
    console.log('🗑️ 删除任务:', taskId);
    
    const response = await fetch(`${this.baseUrl}/api/v1/task/delete`, {
      method: 'DELETE',
      headers: {
        'X-Task-ID': taskId,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`删除任务失败: ${response.status} ${errorText}`);
    }

    const result: APIResponse<any> = await response.json();
    
    if (result.code !== 200) {
      throw new Error(`删除任务失败: ${result.message || '未知错误'}`);
    }

    console.log('✅ 任务删除成功');
  }

  // 创建视频处理任务的便捷方法
  async createVideoProcessingTask(videoFileName?: string): Promise<TaskResponse> {
    const taskName = videoFileName 
      ? `视频处理任务 - ${videoFileName}`
      : `视频处理任务 - ${new Date().toLocaleString()}`;

    return this.createTask({
      name: taskName,
      description: '前端视频处理和音频识别任务',
      config: {
        source: 'webcodecs-frontend',
        enable_audio: true,
        enable_video_analysis: true
      }
    });
  }
}

// 导出默认实例
export const taskManager = new TaskManager();
