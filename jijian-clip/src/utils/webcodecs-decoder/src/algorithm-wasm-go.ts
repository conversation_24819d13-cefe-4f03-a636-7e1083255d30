/**
 * Go WASM 视频分段算法模块接口
 */

import { FrameDifference } from './types.js';

export interface SegmentConfig {
  min_avg_duration?: number;
  max_avg_duration?: number;
  initial_pen?: number;
  max_iterations?: number;
  min_size?: number;
}

export interface SegmentResult {
  segments: number[];
  debug_info: {
    iterations: number;
    final_pen: number;
    avg_durations: number[];
    segment_counts: number[];
  };
  combined_scores: number[];
  success: boolean;
  error?: string;
}

export class GoAlgorithmProcessor {
  private wasmInstance: WebAssembly.Instance | null = null;
  private go: any = null;
  private isInitialized: boolean = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    console.log('🔧 正在初始化 Go WASM...');

    try {
      // 加载 wasm_exec.js
      await this.loadWasmExec();

      // 创建 Go 实例
      this.go = new (window as any).Go();

      // 加载 WASM 模块
      console.log('📦 正在加载 Go WASM 模块...');
      const wasmResponse = await fetch('/segment-go.wasm');
      if (!wasmResponse.ok) {
        throw new Error(`Failed to fetch WASM: ${wasmResponse.status}`);
      }

      const wasmBytes = await wasmResponse.arrayBuffer();
      const wasmModule = await WebAssembly.instantiate(wasmBytes, this.go.importObject);
      
      this.wasmInstance = wasmModule.instance;

      // 运行 Go 程序
      this.go.run(this.wasmInstance);

      console.log('✅ Go WASM 算法模块初始化完成');
      this.isInitialized = true;
    } catch (error) {
      console.error('❌ Go WASM 初始化失败:', error);
      throw error;
    }
  }

  private async loadWasmExec(): Promise<void> {
    return new Promise((resolve, reject) => {
      // 检查是否已经加载
      if (typeof (window as any).Go !== 'undefined') {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = '/wasm_exec.js';
      script.async = true;
      
      script.onload = () => {
        if (typeof (window as any).Go !== 'undefined') {
          console.log('✅ wasm_exec.js 加载完成');
          resolve();
        } else {
          reject(new Error('Go class not found after loading wasm_exec.js'));
        }
      };

      script.onerror = () => reject(new Error('Failed to load wasm_exec.js'));
      document.head.appendChild(script);
    });
  }

  async processFeatures(
    frameDifferences: FrameDifference[],
    config: SegmentConfig = {}
  ): Promise<SegmentResult> {
    await this.initialize();

    const features = frameDifferences.map(diff => [
      diff.colorDiff,
      diff.motionDiff,
      diff.textureDiff
    ]);

    const timestamps = frameDifferences.map(diff => diff.timestamp);

    // 设置默认配置
    const algorithmConfig = {
      min_avg_duration: config.min_avg_duration || 8,
      max_avg_duration: config.max_avg_duration || 50,
      initial_pen: config.initial_pen || 5,
      max_iterations: config.max_iterations || 20,
      min_size: config.min_size || 10,
    };

    console.log('Go 算法配置:', algorithmConfig);

    try {
      // 调用 Go WASM 函数
      const resultJson = (window as any).processFeaturesToSegments(
        JSON.stringify(features),
        JSON.stringify(timestamps),
        JSON.stringify(algorithmConfig)
      );

      const result: SegmentResult = JSON.parse(resultJson);

      if (!result.success) {
        throw new Error(result.error || 'Go 算法处理失败');
      }

      console.log('✅ Go 算法处理完成:', {
        segments: result.segments.length,
        iterations: result.debug_info.iterations,
        final_pen: result.debug_info.final_pen
      });

      return result;
    } catch (error) {
      console.error('❌ Go 算法处理失败:', error);
      throw error;
    }
  }

  isReady(): boolean {
    return this.isInitialized;
  }

  // 获取算法类型标识
  getAlgorithmType(): string {
    return 'go-wasm';
  }
}

let goAlgorithmProcessorInstance: GoAlgorithmProcessor | null = null;

export function getGoAlgorithmProcessor(): GoAlgorithmProcessor {
  if (!goAlgorithmProcessorInstance) {
    goAlgorithmProcessorInstance = new GoAlgorithmProcessor();
  }
  return goAlgorithmProcessorInstance;
}
