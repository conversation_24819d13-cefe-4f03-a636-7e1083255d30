// 音视频融合处理器 - 前端实现
// 完全参考后端 eino/video_segmentation/fusion/processor.go

import { ASRResult, ASRSentence } from './audio-processor.js';

export interface FusionConfig {
  dialogueProtectionMargin: number; // 对话保护边界（秒）
  minSegmentDuration: number; // 最小分段时长（秒）
}

export interface FusionResult {
  adjustedBoundaries: number[]; // 调整后的分段边界点
  totalSegments: number; // 总分段数
  processingTime: number; // 处理时间（毫秒）
}

export class FusionProcessor {
  private config: FusionConfig;

  constructor(config?: Partial<FusionConfig>) {
    // 默认配置，完全参考后端Go配置
    this.config = {
      dialogueProtectionMargin: config?.dialogueProtectionMargin ?? 0.5, // 0.5秒对话保护边界
      minSegmentDuration: config?.minSegmentDuration ?? 1.0, // 1.0秒最小分段时长（与Go一致）
    };
  }

  /**
   * 主要融合方法：根据音频内容调整视觉分段边界
   * @param visualBoundaries 视觉分段边界点
   * @param asrResult ASR识别结果
   * @returns 融合结果
   */
  public fuseBoundaries(visualBoundaries: number[], asrResult?: ASRResult): FusionResult {
    const startTime = performance.now();
    
    console.log('🔄 开始音视频融合处理...');
    console.log('📊 输入数据:', {
      visualBoundaries: visualBoundaries.length,
      sentences: asrResult?.sentences?.length || 0
    });

    // 如果没有音频内容，仍需要对原始边界进行去重和过滤处理
    if (!asrResult || !asrResult.sentences || asrResult.sentences.length === 0) {
      console.log('ℹ️ 无音频内容，对原始边界进行清理处理');

      // 即使没有音频，也要进行去重和过滤，确保边界点有效
      const cleanedBoundaries = this.cleanBoundaries(visualBoundaries);

      return {
        adjustedBoundaries: cleanedBoundaries,
        totalSegments: Math.max(0, cleanedBoundaries.length - 1),
        processingTime: performance.now() - startTime
      };
    }

    // 调整边界点
    const adjustedBoundaries = this.adjustVisualBoundaries(visualBoundaries, asrResult.sentences);
    
    const processingTime = performance.now() - startTime;
    console.log('✅ 音视频融合完成:', {
      originalBoundaries: visualBoundaries.length,
      adjustedBoundaries: adjustedBoundaries.length,
      totalSegments: Math.max(0, adjustedBoundaries.length - 1),
      processingTime: `${processingTime.toFixed(2)}ms`
    });

    return {
      adjustedBoundaries,
      totalSegments: Math.max(0, adjustedBoundaries.length - 1),
      processingTime
    };
  }

  /**
   * 调整视觉边界点以避免切分对话（完全参考Go版本）
   * @param visualBoundaries 原始视觉边界点
   * @param sentences 音频句子
   * @returns 调整后的边界点
   */
  private adjustVisualBoundaries(visualBoundaries: number[], sentences: ASRSentence[]): number[] {
    if (visualBoundaries.length < 2) {
      return visualBoundaries;
    }

    // 处理中间边界点（跳过第一个和最后一个边界）
    const middleBoundaries: number[] = [];

    for (let i = 1; i < visualBoundaries.length - 1; i++) {
      const currentBoundary = visualBoundaries[i];

      // 检查是否切分对话
      if (this.cutsDialogue(currentBoundary, sentences)) {
        // 直接找到最近的句子开始或结束时间（与Go版本完全一致）
        const closestBoundary = this.findClosestSentenceBoundary(currentBoundary, sentences);
        middleBoundaries.push(closestBoundary);
        console.log(`🔧 调整边界: ${currentBoundary.toFixed(2)}s -> ${closestBoundary.toFixed(2)}s (避免切分对话)`);
      } else {
        // 不切分对话，保留原边界
        middleBoundaries.push(currentBoundary);
      }
    }

    // 去重并排序中间边界点（Python第一次去重）
    const uniqueMiddleBoundaries = this.deduplicateAndSort(middleBoundaries);

    // 删除距离太近的中间分段点（Python优化分段时长算法）
    const filteredMiddleBoundaries = this.removeCloseBoundaries(uniqueMiddleBoundaries);

    // 最后添加视频开始和结束边界
    const adjustedBoundaries = [
      visualBoundaries[0], // 视频开始
      ...filteredMiddleBoundaries, // 中间边界点
      visualBoundaries[visualBoundaries.length - 1] // 视频结束
    ];

    // 最终去重并排序（Python第二次去重）
    return this.deduplicateAndSort(adjustedBoundaries);
  }

  /**
   * 检查边界点是否切分对话（完全参考Go版本）
   * @param boundary 边界点时间
   * @param sentences 音频句子
   * @returns 是否切分对话
   */
  private cutsDialogue(boundary: number, sentences: ASRSentence[]): boolean {
    const margin = this.config.dialogueProtectionMargin;

    for (const sentence of sentences) {
      // 如果边界点在对话中间或保护边界内，则认为切分了对话
      // 完全参考Go代码: if (sentence.StartTime-margin) < boundary && boundary < (sentence.EndTime+margin)
      if ((sentence.startTime - margin) < boundary && boundary < (sentence.endTime + margin)) {
        return true;
      }
    }
    return false;
  }



  /**
   * 清理边界点：去重和过滤过近的点（用于无音频情况）
   * @param boundaries 原始边界点
   * @returns 清理后的边界点
   */
  private cleanBoundaries(boundaries: number[]): number[] {
    if (boundaries.length < 2) {
      return boundaries;
    }

    console.log(`🧹 清理边界点: 输入${boundaries.length}个边界点`);

    // 第一步：去重并排序
    let cleanedBoundaries = this.deduplicateAndSort(boundaries);
    console.log(`🧹 去重后: ${cleanedBoundaries.length}个边界点`);

    // 第二步：删除距离太近的边界点
    cleanedBoundaries = this.removeCloseBoundaries(cleanedBoundaries);
    console.log(`🧹 过滤后: ${cleanedBoundaries.length}个边界点`);

    return cleanedBoundaries;
  }

  /**
   * 直接在句子中找到最近的开始或结束时间（完全参考Go版本）
   * @param target 目标时间
   * @param sentences 音频句子
   * @returns 最近的边界时间
   */
  private findClosestSentenceBoundary(target: number, sentences: ASRSentence[]): number {
    if (sentences.length === 0) {
      return target; // 如果没有句子，保持原边界
    }

    let closest = sentences[0].startTime;
    let minDistance = Math.abs(target - closest);

    // 检查所有句子的开始和结束时间（与Go版本完全一致）
    for (const sentence of sentences) {
      // 检查句子开始时间
      const startDistance = Math.abs(target - sentence.startTime);
      if (startDistance < minDistance) {
        minDistance = startDistance;
        closest = sentence.startTime;
      }

      // 检查句子结束时间
      const endDistance = Math.abs(target - sentence.endTime);
      if (endDistance < minDistance) {
        minDistance = endDistance;
        closest = sentence.endTime;
      }
    }

    return closest;
  }

  /**
   * 删除距离太近的分段点（完全参考Go版本）
   * @param boundaries 边界点数组
   * @returns 过滤后的边界点
   */
  private removeCloseBoundaries(boundaries: number[]): number[] {
    if (boundaries.length <= 1) {
      return boundaries;
    }

    const result: number[] = [boundaries[0]];

    for (let i = 1; i < boundaries.length; i++) {
      // 如果当前边界与前一个边界的距离大于等于最小分段时长，则保留
      if (boundaries[i] - result[result.length - 1] >= this.config.minSegmentDuration) {
        result.push(boundaries[i]);
      } else {
        console.log(`🗑️ 删除过近边界: ${boundaries[i].toFixed(2)}s (距离前一个边界: ${(boundaries[i] - result[result.length - 1]).toFixed(2)}s)`);
      }
    }

    return result;
  }

  /**
   * 去重并排序（完全参考Go版本）
   * @param boundaries 边界点数组
   * @returns 去重排序后的边界点
   */
  private deduplicateAndSort(boundaries: number[]): number[] {
    const seen = new Map<number, boolean>();
    const unique: number[] = [];

    for (const boundary of boundaries) {
      if (!seen.has(boundary)) {
        unique.push(boundary);
        seen.set(boundary, true);
      }
    }

    // 排序（与Go的sort.Float64s一致）
    unique.sort((a, b) => a - b);
    return unique;
  }



  /**
   * 获取默认融合配置（完全参考Go版本）
   */
  public static getDefaultConfig(): FusionConfig {
    return {
      dialogueProtectionMargin: 0.5, // 与Go配置一致
      minSegmentDuration: 1.0, // 与Go配置一致
    };
  }
}
