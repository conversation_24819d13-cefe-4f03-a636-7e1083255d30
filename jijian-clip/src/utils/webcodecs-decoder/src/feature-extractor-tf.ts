// 使用全局TensorFlow.js (从CDN加载)
declare const tf: any;
import { FrameDifference } from './types.js';

// 简单类型定义
type Tensor3D = any;
type Tensor2D = any;

export class FeatureExtractor {
  private prevFrame: Tensor3D | null = null;
  private isTFReady: boolean = false;
  private textureKernel: Tensor2D | null = null; // Sobel算子核
  private readonly RESIZE_WIDTH = 256; // GPU加速的优化尺寸
  private frameIndex: number = 0;

  // 🔥 重用资源，避免重复创建
  private reusableCanvas: HTMLCanvasElement | null = null;
  private reusableCtx: CanvasRenderingContext2D | null = null;

  // 预计算的Sobel算子，避免重复reshape和transpose
  private sobelXKernel: any = null;
  private sobelYKernel: any = null;

  // 性能统计
  private processedFrames: number = 0;
  private totalProcessingTime: number = 0;

  constructor() {
    // 延迟初始化，等待TensorFlow.js准备就绪
  }

  // 时间戳精度处理函数（与批处理器保持一致）
  private roundTime(value: number, precision: number = 3): number {
    const multiplier = Math.pow(10, precision);
    return Math.round(value * multiplier) / multiplier;
  }

  async initialize(): Promise<void> {
    await this.loadTF();

    // 初始化Sobel算子核
    this.textureKernel = tf.tensor2d([
      [-1, 0, 1],
      [-2, 0, 2],
      [-1, 0, 1]
    ], [3, 3], 'float32');

    // 🔥 预计算Sobel算子，避免重复reshape和transpose
    this.sobelXKernel = this.textureKernel.reshape([3, 3, 1, 1]);
    this.sobelYKernel = this.textureKernel.transpose().reshape([3, 3, 1, 1]);

    // 🔥 初始化重用的canvas
    this.reusableCanvas = document.createElement('canvas');
    this.reusableCtx = this.reusableCanvas.getContext('2d')!;

    console.log('✅ 极简GPU特征提取器已启用');
  }

  private async loadTF(): Promise<void> {
    try {
      // 检查是否已经初始化
      if (this.isTFReady) {
        return;
      }

      // 检查当前后端，如果已经是webgl就不重复设置
      const currentBackend = tf.getBackend();
      if (currentBackend !== 'webgl') {
        console.log(`🔄 切换TensorFlow.js后端从 ${currentBackend} 到 webgl`);
        await tf.setBackend('webgl');
      }

      await tf.ready();
      this.isTFReady = true;
      console.log(`✅ TensorFlow.js ${tf.getBackend()} backend ready`);
    } catch (error) {
      console.error('❌ TensorFlow.js initialization failed:', error);
      throw error;
    }
  }

  async processFrame(videoFrame: VideoFrame): Promise<FrameDifference | null> {
    if (!this.isTFReady) {
      console.log('🎯 TensorFlow.js 未就绪，正在初始化...');
      await this.loadTF();
    }

    const frameStartTime = performance.now();
    let currentTensor: any = null;

    try {
      console.log(`🎯 开始处理帧 ${this.frameIndex}...`);
      currentTensor = await this.videoFrameToTensor(videoFrame);
      console.log(`🎯 帧 ${this.frameIndex} 转换为张量完成`);

      // 第一帧处理
      if (!this.prevFrame) {
        console.log('🎯 第一帧，保存为参考帧');
        this.prevFrame = currentTensor;
        this.frameIndex++;
        return null;
      }

      // GPU并行计算所有特征
      console.log(`🎯 开始计算帧 ${this.frameIndex} 的特征...`);
      const [colorDiff, motionDiff, textureDiff] = await Promise.all([
        this.calculateColorDifference(this.prevFrame, currentTensor),
        this.calculateMotionDifference(this.prevFrame, currentTensor),
        this.calculateTextureDifference(this.prevFrame, currentTensor)
      ]);

      console.log(`🎯 帧 ${this.frameIndex} 特征计算完成:`, {
        colorDiff: colorDiff.toFixed(4),
        motionDiff: motionDiff.toFixed(4),
        textureDiff: textureDiff.toFixed(4)
      });

      // 更新帧并清理
      this.prevFrame.dispose();
      this.prevFrame = currentTensor;
      currentTensor = null; // 防止在finally中重复释放

      // 🔥 性能统计和内存监控
      const frameTime = performance.now() - frameStartTime;
      this.processedFrames++;
      this.totalProcessingTime += frameTime;

      // 每10帧输出一次内存状态
      if (this.processedFrames % 10 === 0) {
        const avgTime = this.totalProcessingTime / this.processedFrames;
        const memory = tf.memory();
        console.log(`📊 逐帧处理统计: ${this.processedFrames} 帧, 平均 ${avgTime.toFixed(1)}ms/帧`);
        console.log(`🧠 TensorFlow.js 内存: ${memory.numTensors} 张量, ${(memory.numBytes / 1024 / 1024).toFixed(1)}MB`);
      }

      const result = {
        colorDiff,
        motionDiff,
        textureDiff,
        frameIndex: this.frameIndex++,
        timestamp: this.roundTime((videoFrame.timestamp || 0) / 1_000_000) // 转换微秒为秒，保持3位小数精度
      };

      return result;
    } catch (error) {
      console.error('❌ GPU特征提取错误:', error);

      // 🔥 错误处理：确保张量被释放
      if (currentTensor) {
        try {
          currentTensor.dispose();
        } catch (e) {
          console.warn('⚠️ 释放currentTensor时出错:', e);
        }
      }

      this.frameIndex++;
      return null;
    }
  }

  private async videoFrameToTensor(frame: VideoFrame): Promise<Tensor3D> {
    try {
      // 🔥 使用重用的canvas，避免重复创建
      if (!this.reusableCanvas || !this.reusableCtx) {
        throw new Error('Canvas not initialized');
      }

      // 调整canvas尺寸
      this.reusableCanvas.width = frame.displayWidth;
      this.reusableCanvas.height = frame.displayHeight;

      // 直接绘制VideoFrame到canvas
      this.reusableCtx.drawImage(frame, 0, 0);

      // 转换为张量并调整尺寸以优化GPU性能
      return tf.tidy(() => {
        const tensor = tf.browser.fromPixels(this.reusableCanvas!);
        const aspectRatio = frame.displayHeight / frame.displayWidth;
        const targetHeight = Math.round(this.RESIZE_WIDTH * aspectRatio);
        return tf.image.resizeBilinear(tensor, [targetHeight, this.RESIZE_WIDTH]);
      });
    } catch (error) {
      console.error('❌ VideoFrame转张量失败:', error);
      throw error;
    }
  }

  private async calculateColorDifference(prev: Tensor3D, curr: Tensor3D): Promise<number> {
    return tf.tidy(() => {
      // 简化的RGB颜色差异计算
      const [prevR, prevG, prevB] = tf.split(prev, 3, 2);
      const [currR, currG, currB] = tf.split(curr, 3, 2);

      // 计算每个通道的均值
      const prevRMean = tf.mean(prevR);
      const prevGMean = tf.mean(prevG);
      const prevBMean = tf.mean(prevB);

      const currRMean = tf.mean(currR);
      const currGMean = tf.mean(currG);
      const currBMean = tf.mean(currB);

      // 计算欧几里得距离
      const rDiff = tf.square(tf.sub(currRMean, prevRMean));
      const gDiff = tf.square(tf.sub(currGMean, prevGMean));
      const bDiff = tf.square(tf.sub(currBMean, prevBMean));

      const colorDiff = tf.sqrt(tf.add(tf.add(rDiff, gDiff), bDiff));

      return colorDiff.dataSync()[0] / 255.0; // 归一化
    });
  }



  private async calculateMotionDifference(prev: Tensor3D, curr: Tensor3D): Promise<number> {
    return tf.tidy(() => {
      // 转换为灰度图
      const prevGray = this.rgbToGrayscale(prev);
      const currGray = this.rgbToGrayscale(curr);
      
      // 计算帧间绝对差异
      const diff = tf.abs(prevGray.sub(currGray));
      return diff.mean().dataSync()[0];
    });
  }

  private rgbToGrayscale(rgb: Tensor3D): Tensor2D {
    return tf.tidy(() => {
      const [r, g, b] = tf.split(rgb, 3, 2);
      return r.mul(0.299).add(g.mul(0.587)).add(b.mul(0.114)).squeeze();
    });
  }

  private async calculateTextureDifference(prev: Tensor3D, curr: Tensor3D): Promise<number> {
    return tf.tidy(() => {
      const prevGray = this.rgbToGrayscale(prev);
      const currGray = this.rgbToGrayscale(curr);
      
      // GPU加速的Sobel边缘检测
      const sobelPrev = this.sobelEdgeDetection(prevGray);
      const sobelCurr = this.sobelEdgeDetection(currGray);
      
      // 计算纹理差异
      const diff = tf.abs(sobelPrev.sub(sobelCurr));
      return diff.mean().dataSync()[0];
    });
  }

  private sobelEdgeDetection(gray: Tensor2D): Tensor2D {
    return tf.tidy(() => {
      // 🔥 使用预计算的Sobel算子，避免重复reshape和transpose
      if (!this.sobelXKernel || !this.sobelYKernel) {
        throw new Error('Sobel kernels not initialized');
      }

      // 添加批处理和通道维度
      const batched = gray.reshape([1, ...gray.shape, 1]);

      // 应用预计算的Sobel算子
      const gx = tf.conv2d(batched, this.sobelXKernel, 1, 'same');
      const gy = tf.conv2d(batched, this.sobelYKernel, 1, 'same');

      // 计算梯度幅值
      return tf.sqrt(gx.square().add(gy.square())).squeeze();
    });
  }

  dispose(): void {
    console.log('🧹 开始清理逐帧特征提取器...');

    // 释放GPU张量
    if (this.prevFrame) {
      this.prevFrame.dispose();
      this.prevFrame = null;
    }
    if (this.textureKernel) {
      this.textureKernel.dispose();
      this.textureKernel = null;
    }

    // 🔥 释放预计算的Sobel算子
    if (this.sobelXKernel) {
      this.sobelXKernel.dispose();
      this.sobelXKernel = null;
    }
    if (this.sobelYKernel) {
      this.sobelYKernel.dispose();
      this.sobelYKernel = null;
    }

    // 🔥 清理重用的canvas
    if (this.reusableCanvas) {
      // 清空canvas内容
      if (this.reusableCtx) {
        this.reusableCtx.clearRect(0, 0, this.reusableCanvas.width, this.reusableCanvas.height);
      }
      this.reusableCanvas = null;
      this.reusableCtx = null;
    }

    // 清理TensorFlow.js变量
    tf.disposeVariables();

    // 打印最终统计和内存状态
    if (this.processedFrames > 0) {
      const avgTime = this.totalProcessingTime / this.processedFrames;
      console.log(`📊 逐帧处理最终统计: ${this.processedFrames} 帧, 平均 ${avgTime.toFixed(1)}ms/帧`);
    }

    const finalMemory = tf.memory();
    console.log(`🧠 清理后TensorFlow.js内存: ${finalMemory.numTensors} 张量, ${(finalMemory.numBytes / 1024 / 1024).toFixed(1)}MB`);
  }
}