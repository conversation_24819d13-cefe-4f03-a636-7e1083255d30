// 简洁的OpenCV.js特征提取器 - 完全参考Python实现
declare const cv: any;
import { FrameDifference } from './types.js';

export class FeatureExtractor {
  private prevFrame: any = null; // cv.Mat
  private isOpenCVReady: boolean = false;
  private frameIndex: number = 0;

  // 时间戳精度处理函数（与批处理器保持一致）
  private roundTime(value: number, precision: number = 3): number {
    const multiplier = Math.pow(10, precision);
    return Math.round(value * multiplier) / multiplier;
  }

  constructor() {
    // 延迟初始化
  }

  async initialize(): Promise<void> {
    await this.loadOpenCV();
    console.log('✅ OpenCV.js 特征提取器已启用');
  }

  private loadOpenCV(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (typeof cv !== 'undefined' && cv.Mat) {
        this.isOpenCVReady = true;
        console.log('✅ OpenCV.js already loaded');
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://docs.opencv.org/4.8.0/opencv.js';
      script.async = true;
      
      script.onload = () => {
        if (typeof cv !== 'undefined') {
          cv.onRuntimeInitialized = () => {
            this.isOpenCVReady = true;
            console.log('✅ OpenCV.js loaded and initialized');
            resolve();
          };
        } else {
          reject(new Error('OpenCV.js failed to load'));
        }
      };

      script.onerror = () => reject(new Error('Failed to load OpenCV.js script'));
      document.head.appendChild(script);
    });
  }

  async processFrame(videoFrame: VideoFrame): Promise<FrameDifference | null> {
    try {
      if (!this.isOpenCVReady) {
        console.log('🎯 OpenCV.js 未就绪，正在初始化...');
        await this.loadOpenCV();
      }

      console.log(`🎯 OpenCV开始处理帧 ${this.frameIndex}...`);
      // 转换VideoFrame为OpenCV Mat
      const currentFrame = this.videoFrameToMat(videoFrame);
      console.log(`🎯 帧 ${this.frameIndex} 转换为Mat完成`);

      // 如果是第一帧，保存并返回null
      if (!this.prevFrame) {
        console.log('🎯 第一帧，保存为参考帧');
        this.prevFrame = currentFrame.clone();
        currentFrame.delete();
        this.frameIndex++;
        return null; // 第一帧没有差异
      }

      // 计算三种特征差异 - 完全参考Python实现
      console.log(`🎯 开始计算帧 ${this.frameIndex} 的特征...`);
      const colorDiff = this.calculateColorDifference(this.prevFrame, currentFrame);
      const motionDiff = this.calculateMotionDifference(this.prevFrame, currentFrame);
      const textureDiff = this.calculateTextureDifference(this.prevFrame, currentFrame);

      console.log(`🎯 帧 ${this.frameIndex} 特征计算完成:`, {
        colorDiff: colorDiff.toFixed(4),
        motionDiff: motionDiff.toFixed(4),
        textureDiff: textureDiff.toFixed(4)
      });

      // 更新前一帧
      this.prevFrame.delete();
      this.prevFrame = currentFrame.clone();
      currentFrame.delete();

      const result = {
        colorDiff,
        motionDiff,
        textureDiff,
        frameIndex: this.frameIndex++,
        timestamp: this.roundTime((videoFrame.timestamp || 0) / 1_000_000) // 转换微秒为秒，保持3位小数精度
      };

      return result;

    } catch (error) {
      console.error('❌ OpenCV特征提取错误:', error);
      this.frameIndex++;
      return null;
    }
  }

  private videoFrameToMat(videoFrame: VideoFrame): any {
    // 创建Canvas并绘制VideoFrame
    const canvas = document.createElement('canvas');
    canvas.width = videoFrame.displayWidth;
    canvas.height = videoFrame.displayHeight;
    const ctx = canvas.getContext('2d')!;
    ctx.drawImage(videoFrame, 0, 0);
    
    // 转换为OpenCV Mat
    return cv.imread(canvas);
  }

  // 颜色差异 - 完全参考Python实现
  private calculateColorDifference(prev: any, curr: any): number {
    // 转换为HSV
    const prevHSV = new cv.Mat();
    const currHSV = new cv.Mat();
    cv.cvtColor(prev, prevHSV, cv.COLOR_RGB2HSV);
    cv.cvtColor(curr, currHSV, cv.COLOR_RGB2HSV);
    
    // 计算HSV直方图 [180, 256] - 与Python一致
    const histPrev = new cv.Mat();
    const histCurr = new cv.Mat();
    const histSize = [180, 256];
    const ranges = [0, 180, 0, 256];
    const channels = [0, 1]; // H和S通道
    const mask = new cv.Mat();
    
    const matVectorPrev = new cv.MatVector();
    matVectorPrev.push_back(prevHSV);
    cv.calcHist(matVectorPrev, channels, mask, histPrev, histSize, ranges);
    matVectorPrev.delete();
    
    const matVectorCurr = new cv.MatVector();
    matVectorCurr.push_back(currHSV);
    cv.calcHist(matVectorCurr, channels, mask, histCurr, histSize, ranges);
    matVectorCurr.delete();
    
    // 归一化 - 与Python一致
    cv.normalize(histPrev, histPrev);
    cv.normalize(histCurr, histCurr);
    
    // 巴氏距离 - 与Python一致
    const distance = cv.compareHist(histPrev, histCurr, cv.HISTCMP_BHATTACHARYYA);
    
    // 清理内存
    prevHSV.delete();
    currHSV.delete();
    histPrev.delete();
    histCurr.delete();
    mask.delete();
    
    return distance;
  }

  // 运动差异 - 直接使用原始尺寸
  private calculateMotionDifference(prev: any, curr: any): number {
    // 转灰度
    const prevGray = new cv.Mat();
    const currGray = new cv.Mat();
    cv.cvtColor(prev, prevGray, cv.COLOR_RGB2GRAY);
    cv.cvtColor(curr, currGray, cv.COLOR_RGB2GRAY);

    // Farneback光流 - 参数与Python一致
    const flow = new cv.Mat();
    cv.calcOpticalFlowFarneback(prevGray, currGray, flow, 0.5, 3, 15, 3, 5, 1.2, 0);

    // 计算幅值平均
    const flowData = flow.data32F;
    let sum = 0;
    for (let i = 0; i < flowData.length; i += 2) {
      const u = flowData[i];
      const v = flowData[i + 1];
      sum += Math.sqrt(u * u + v * v);
    }
    const motionDiff = sum / (flowData.length / 2);

    // 清理内存
    prevGray.delete();
    currGray.delete();
    flow.delete();

    return motionDiff;
  }

  // 纹理差异 - 使用Sobel边缘检测
  private calculateTextureDifference(prev: any, curr: any): number {
    // 转灰度
    const prevGray = new cv.Mat();
    const currGray = new cv.Mat();
    cv.cvtColor(prev, prevGray, cv.COLOR_RGB2GRAY);
    cv.cvtColor(curr, currGray, cv.COLOR_RGB2GRAY);

    // 计算Sobel边缘
    const sobelPrev = new cv.Mat();
    const sobelCurr = new cv.Mat();
    cv.Sobel(prevGray, sobelPrev, cv.CV_32F, 1, 1, 3);
    cv.Sobel(currGray, sobelCurr, cv.CV_32F, 1, 1, 3);

    // 计算直方图
    const histPrev = new cv.Mat();
    const histCurr = new cv.Mat();
    const histSize = [256];
    const ranges = [0, 256];
    const channels = [0];
    const mask = new cv.Mat();

    const matVectorPrev = new cv.MatVector();
    matVectorPrev.push_back(sobelPrev);
    cv.calcHist(matVectorPrev, channels, mask, histPrev, histSize, ranges);
    matVectorPrev.delete();

    const matVectorCurr = new cv.MatVector();
    matVectorCurr.push_back(sobelCurr);
    cv.calcHist(matVectorCurr, channels, mask, histCurr, histSize, ranges);
    matVectorCurr.delete();

    // 归一化
    cv.normalize(histPrev, histPrev);
    cv.normalize(histCurr, histCurr);

    // 卡方距离
    const distance = cv.compareHist(histPrev, histCurr, cv.HISTCMP_CHISQR);

    // 清理内存
    prevGray.delete();
    currGray.delete();
    sobelPrev.delete();
    sobelCurr.delete();
    histPrev.delete();
    histCurr.delete();
    mask.delete();

    return distance;
  }

  dispose(): void {
    if (this.prevFrame) {
      this.prevFrame.delete();
      this.prevFrame = null;
    }
  }
}
