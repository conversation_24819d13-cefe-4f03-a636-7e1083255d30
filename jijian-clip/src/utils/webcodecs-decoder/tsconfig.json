{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "node", "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker"], "types": ["@webgpu/types"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}