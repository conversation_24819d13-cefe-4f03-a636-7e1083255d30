{"name": "webcodecs-decoder", "version": "1.0.0", "description": "WebCodecs hardware video decoder module", "main": "dist/decoder.js", "scripts": {"build": "tsc", "dev": "tsc --watch", "serve": "http-server . -p 8090 -c-1", "start": "npm run build && npm run serve", "build-go": "cd go && ./build.sh", "build-py": "cd python && ./build.sh"}, "devDependencies": {"@webgpu/types": "^0.1.63", "http-server": "^14.1.1", "typescript": "^5.0.0"}, "dependencies": {"mp4box": "^0.5.2", "@tensorflow/tfjs": "^4.15.0", "@tensorflow/tfjs-backend-webgpu": "^4.15.0", "@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1"}}