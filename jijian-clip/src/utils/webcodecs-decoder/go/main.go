//go:build js && wasm
// +build js,wasm

package main

import (
	"encoding/json"
	"fmt"
	"math"
	"syscall/js"
)

// ===== 数据结构定义 =====

type SegmentConfig struct {
	MinAvgDuration float64 `json:"min_avg_duration"`
	MaxAvgDuration float64 `json:"max_avg_duration"`
	InitialPen     float64 `json:"initial_pen"`
	MaxIterations  int     `json:"max_iterations"`
	MinSize        int     `json:"min_size"`
}

type DebugInfo struct {
	Iterations    int       `json:"iterations"`
	FinalPen      float64   `json:"final_pen"`
	AvgDurations  []float64 `json:"avg_durations"`
	SegmentCounts []int     `json:"segment_counts"`
}

type SegmentResult struct {
	Segments       []float64 `json:"segments"`
	DebugInfo      DebugInfo `json:"debug_info"`
	CombinedScores []float64 `json:"combined_scores"`
	Success        bool      `json:"success"`
	Error          string    `json:"error,omitempty"`
}

// ===== 算法处理器 =====

type SegmentProcessor struct {
	config SegmentConfig
}

func NewSegmentProcessor(config SegmentConfig) *SegmentProcessor {
	// 设置默认值
	if config.MinAvgDuration == 0 {
		config.MinAvgDuration = 8
	}
	if config.MaxAvgDuration == 0 {
		config.MaxAvgDuration = 50
	}
	if config.InitialPen == 0 {
		config.InitialPen = 5
	}
	if config.MaxIterations == 0 {
		config.MaxIterations = 20
	}
	if config.MinSize == 0 {
		config.MinSize = 2
	}

	return &SegmentProcessor{config: config}
}

// ===== 工具函数 =====

// CalculateL2Norm 计算向量的L2范数
func CalculateL2Norm(vector []float64) float64 {
	sumSquares := 0.0
	for _, val := range vector {
		sumSquares += val * val
	}
	return math.Sqrt(sumSquares)
}

// calculateMean 计算平均值
func calculateMean(data []float64) float64 {
	if len(data) == 0 {
		return 0
	}
	sum := 0.0
	for _, val := range data {
		sum += val
	}
	return sum / float64(len(data))
}

// calculateStdDev 计算标准差
func calculateStdDev(data []float64, mean float64) float64 {
	if len(data) == 0 {
		return 0
	}
	sumSquares := 0.0
	for _, val := range data {
		diff := val - mean
		sumSquares += diff * diff
	}
	variance := sumSquares / float64(len(data))
	return math.Sqrt(variance)
}

// calculateDifferenceFromTarget 计算与目标平均时长的差距
func (sp *SegmentProcessor) calculateDifferenceFromTarget(avgDuration float64) float64 {
	targetAvg := (sp.config.MinAvgDuration + sp.config.MaxAvgDuration) / 2
	if avgDuration < sp.config.MinAvgDuration {
		return sp.config.MinAvgDuration - avgDuration
	} else if avgDuration > sp.config.MaxAvgDuration {
		return avgDuration - sp.config.MaxAvgDuration
	} else {
		if avgDuration < targetAvg {
			return targetAvg - avgDuration
		}
		return avgDuration - targetAvg
	}
}

// ===== 特征处理函数 =====

func (sp *SegmentProcessor) normalizeAndCombine(features [][]float64) []float64 {
	if len(features) == 0 {
		return []float64{}
	}

	numFeatures := len(features[0])
	if numFeatures == 0 {
		return []float64{}
	}

	// 计算每个特征维度的均值和标准差
	means := make([]float64, numFeatures)
	stds := make([]float64, numFeatures)

	for i := 0; i < numFeatures; i++ {
		column := make([]float64, len(features))
		for j := 0; j < len(features); j++ {
			column[j] = features[j][i]
		}
		means[i] = calculateMean(column)
		stds[i] = calculateStdDev(column, means[i])
		if stds[i] == 0 {
			stds[i] = 1e-10 // 避免除零
		}
	}

	// 归一化并组合特征
	combined := make([]float64, len(features))
	for i := 0; i < len(features); i++ {
		sum := 0.0
		for j := 0; j < numFeatures; j++ {
			normalized := (features[i][j] - means[j]) / stds[j]
			sum += normalized
		}
		combined[i] = sum
	}

	return combined
}

// ===== PELT算法实现 =====

func (sp *SegmentProcessor) customPELTGaussian(
	data []float64,
	minSize int,
	penalty float64,
) []int {
	n := len(data)
	if n < 2 {
		return nil
	}

	// 预计算累加和
	cumSum := make([]float64, n+1)
	cumSumSq := make([]float64, n+1)
	for i := 1; i <= n; i++ {
		cumSum[i] = cumSum[i-1] + data[i-1]
		cumSumSq[i] = cumSumSq[i-1] + data[i-1]*data[i-1]
	}

	dp := make([]float64, n+1)
	dp[0] = -penalty
	lastCP := make([]int, n+1)

	const eps = 1e-6 // 防止 log(0)
	for t := minSize; t <= n; t++ {
		bestCost := math.Inf(1)
		bestTau := 0

		for tau := 0; tau <= t-minSize; tau++ {
			length := t - tau
			sum := cumSum[t] - cumSum[tau]
			sumSq := cumSumSq[t] - cumSumSq[tau]
			mean := sum / float64(length)

			// 1) 计算段内方差 σ²
			variance := (sumSq - 2*mean*sum + mean*mean*float64(length)) / float64(length)
			if variance < eps {
				variance = eps
			}

			// 2) 负对数似然（去掉常数项）：n/2 * ln(σ²)
			segmentCost := 0.5 * float64(length) * math.Log(variance)

			// 3) 加上常数惩罚
			totalCost := dp[tau] + segmentCost + penalty
			if totalCost < bestCost {
				bestCost = totalCost
				bestTau = tau
			}
		}
		dp[t] = bestCost
		lastCP[t] = bestTau
	}

	// 回溯
	cps := []int{}
	cp := n
	for cp > 0 {
		cp = lastCP[cp]
		if cp > 0 {
			cps = append(cps, cp)
		}
	}
	// 反转
	for i, j := 0, len(cps)-1; i < j; i, j = i+1, j-1 {
		cps[i], cps[j] = cps[j], cps[i]
	}
	return cps
}

// ===== 优化算法 =====

func (sp *SegmentProcessor) detectSegmentsCore(
	combinedScores []float64,
	timestamps []float64,
	minSize int,
	pen float64,
) []float64 {
	if len(combinedScores) == 0 {
		return []float64{timestamps[0], timestamps[len(timestamps)-1]}
	}

	changePoints := sp.customPELTGaussian(combinedScores, minSize, pen)

	if len(changePoints) == 0 {
		return []float64{timestamps[0], timestamps[len(timestamps)-1]}
	}

	segments := []float64{timestamps[0]}
	for _, bp := range changePoints {
		if bp > 0 && bp < len(timestamps) {
			segments = append(segments, timestamps[bp])
		}
	}

	if len(segments) == 0 || segments[len(segments)-1] != timestamps[len(timestamps)-1] {
		segments = append(segments, timestamps[len(timestamps)-1])
	}

	return segments
}

func (sp *SegmentProcessor) detectSegmentsWithAutoPenalty(
	combinedScores []float64,
	timestamps []float64,
) ([]float64, DebugInfo) {
	if len(combinedScores) == 0 {
		return []float64{timestamps[0], timestamps[len(timestamps)-1]}, DebugInfo{
			Iterations: 0,
			FinalPen:   sp.config.InitialPen,
		}
	}

	minSize := sp.config.MinSize
	penalty := sp.config.InitialPen
	bestSegments := []float64{}
	bestDiff := float64(1e9)
	debugInfo := DebugInfo{
		Iterations:    0,
		FinalPen:      penalty,
		AvgDurations:  []float64{},
		SegmentCounts: []int{},
	}

	for iteration := 0; iteration < sp.config.MaxIterations; iteration++ {
		debugInfo.Iterations = iteration + 1

		segments := sp.detectSegmentsCore(combinedScores, timestamps, minSize, penalty)

		var avgDuration float64
		if len(segments) > 1 {
			durations := make([]float64, len(segments)-1)
			for i := 0; i < len(segments)-1; i++ {
				durations[i] = segments[i+1] - segments[i]
			}
			avgDuration = calculateMean(durations)
		} else {
			avgDuration = timestamps[len(timestamps)-1] - timestamps[0]
		}

		debugInfo.AvgDurations = append(debugInfo.AvgDurations, avgDuration)
		debugInfo.SegmentCounts = append(debugInfo.SegmentCounts, len(segments)-1)

		diff := sp.calculateDifferenceFromTarget(avgDuration)

		if diff < bestDiff {
			bestSegments = segments
			bestDiff = diff
		}

		if avgDuration >= sp.config.MinAvgDuration && avgDuration <= sp.config.MaxAvgDuration {
			debugInfo.FinalPen = penalty
			return segments, debugInfo
		}

		if avgDuration < sp.config.MinAvgDuration {
			penalty *= 1.5
		} else {
			penalty /= 1.5
		}

		if penalty < 0.1 {
			penalty = 0.1
		}
	}

	if len(bestSegments) == 0 {
		bestSegments = sp.detectSegmentsCore(combinedScores, timestamps, minSize, sp.config.InitialPen)
	}

	debugInfo.FinalPen = penalty
	return bestSegments, debugInfo
}

// ===== WASM导出函数 =====

func processFeaturesToSegments(this js.Value, args []js.Value) interface{} {
	if len(args) < 2 {
		result := SegmentResult{
			Success: false,
			Error:   "需要至少2个参数：features_json 和 timestamps_json",
		}
		resultJSON, _ := json.Marshal(result)
		return string(resultJSON)
	}

	featuresJSON := args[0].String()
	timestampsJSON := args[1].String()
	configJSON := "{}"
	if len(args) > 2 {
		configJSON = args[2].String()
	}

	var features [][]float64
	var timestamps []float64
	var config SegmentConfig

	if err := json.Unmarshal([]byte(featuresJSON), &features); err != nil {
		result := SegmentResult{
			Success: false,
			Error:   fmt.Sprintf("解析特征数据失败: %v", err),
		}
		resultJSON, _ := json.Marshal(result)
		return string(resultJSON)
	}

	if err := json.Unmarshal([]byte(timestampsJSON), &timestamps); err != nil {
		result := SegmentResult{
			Success: false,
			Error:   fmt.Sprintf("解析时间戳数据失败: %v", err),
		}
		resultJSON, _ := json.Marshal(result)
		return string(resultJSON)
	}

	if err := json.Unmarshal([]byte(configJSON), &config); err != nil {
		result := SegmentResult{
			Success: false,
			Error:   fmt.Sprintf("解析配置数据失败: %v", err),
		}
		resultJSON, _ := json.Marshal(result)
		return string(resultJSON)
	}

	processor := NewSegmentProcessor(config)
	combinedScores := processor.normalizeAndCombine(features)
	segments, debugInfo := processor.detectSegmentsWithAutoPenalty(combinedScores, timestamps)

	result := SegmentResult{
		Segments:       segments,
		DebugInfo:      debugInfo,
		CombinedScores: combinedScores,
		Success:        true,
	}

	resultJSON, err := json.Marshal(result)
	if err != nil {
		errorResult := SegmentResult{
			Success: false,
			Error:   fmt.Sprintf("序列化结果失败: %v", err),
		}
		errorJSON, _ := json.Marshal(errorResult)
		return string(errorJSON)
	}

	return string(resultJSON)
}

func main() {
	// 注册WASM函数
	js.Global().Set("processFeaturesToSegments", js.FuncOf(processFeaturesToSegments))

	// 保持程序运行
	select {}
}
