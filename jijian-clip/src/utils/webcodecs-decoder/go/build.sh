#!/bin/bash

# 编译 Go 算法模块为 WASM
set -e

echo "🔧 编译 Go 算法模块为 WASM..."

# 检查 Go 版本
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go 1.23+"
    exit 1
fi

GO_VERSION=$(go version | grep -o 'go[0-9]\+\.[0-9]\+' | sed 's/go//')
echo "📋 当前 Go 版本: $GO_VERSION"

# 设置环境变量
export GOOS=js
export GOARCH=wasm

# 编译为 WASM
echo "🚀 开始编译..."
go build -o segment-go.wasm main.go

# 复制 wasm_exec.js
GOROOT=$(go env GOROOT)
if [ -f "$GOROOT/misc/wasm/wasm_exec.js" ]; then
    cp "$GOROOT/misc/wasm/wasm_exec.js" .
    echo "✅ 已复制 wasm_exec.js"
else
    echo "⚠️  未找到 wasm_exec.js，请手动复制"
fi

# 创建输出目录
mkdir -p ../dist

# 移动结果文件
mv segment-go.wasm ../dist/
cp wasm_exec.js ../dist/ 2>/dev/null || true

echo "✅ Go WASM 模块编译完成:"
echo "   - ../dist/segment-go.wasm"
echo "   - ../dist/wasm_exec.js"

# 显示文件大小
if [ -f "../dist/segment-go.wasm" ]; then
    SIZE=$(du -h "../dist/segment-go.wasm" | cut -f1)
    echo "📦 WASM 文件大小: $SIZE"
fi

echo "🎉 构建完成！"
