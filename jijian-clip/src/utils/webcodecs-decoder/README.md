# WebCodecs 硬件解码器模块

这是一个基于 WebCodecs API 的高性能视频硬件解码模块，用于提取视频的 YUV 帧数据。

## 功能特性

- ✅ **硬件加速解码**: 使用 WebCodecs API 进行硬件加速视频解码
- ✅ **YUV 格式输出**: 直接输出 YUV I420 格式的帧数据
- ✅ **流式处理**: 支持逐帧回调处理，适合大视频文件
- ✅ **多格式支持**: 支持 MP4、WebM 等主流视频格式
- ✅ **简洁 API**: 简单易用的接口设计
- ✅ **TypeScript**: 完整的类型定义支持

## 浏览器兼容性

- Chrome 94+
- Edge 94+
- 需要 HTTPS 环境或 localhost

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 编译项目

```bash
npm run build
```

### 3. 启动测试服务器

```bash
npm start
```

### 4. 打开浏览器测试

访问 http://localhost:60850 进行测试

## API 使用示例

```typescript
import { WebCodecsVideoDecoder } from './dist/decoder.js';

// 创建解码器实例
const decoder = new WebCodecsVideoDecoder({
  onFrame: (yuvFrame) => {
    console.log('收到新帧:', yuvFrame);
    // 处理 YUV 帧数据
  },
  onError: (error) => {
    console.error('解码错误:', error);
  },
  onComplete: () => {
    console.log('解码完成');
  }
});

// 配置解码器
await decoder.configure({
  codec: 'avc1.42E01E', // H.264
  outputFormat: 'I420',
  hardwareAcceleration: 'prefer-hardware'
});

// 开始解码视频文件
await decoder.decodeVideo(videoFile);
```

## 数据结构

### YUVFrame

```typescript
interface YUVFrame {
  yData: Uint8Array;    // Y 分量数据
  uData: Uint8Array;    // U 分量数据  
  vData: Uint8Array;    // V 分量数据
  width: number;        // 帧宽度
  height: number;       // 帧高度
  timestamp: number;    // 时间戳 (微秒)
  format: 'I420';       // YUV 格式
  index?: number;       // 帧索引
}
```

## 性能优势

相比传统的 Canvas 解码方案:

- **解码性能**: 3-5倍提升 (硬件加速)
- **内存效率**: 直接输出 YUV，无需 RGB 转换
- **CPU 占用**: 大幅降低，解码工作交给专用硬件
- **延迟**: 更低的处理延迟

## 注意事项

1. **HTTPS 要求**: WebCodecs API 需要安全上下文 (HTTPS 或 localhost)
2. **浏览器支持**: 目前仅支持 Chromium 内核浏览器
3. **编码格式**: 自动检测，支持 H.264、VP9 等主流格式
4. **内存管理**: 记得调用 `dispose()` 清理资源

## 项目结构

```
webcodecs-decoder/
├── src/
│   ├── types.ts      # 类型定义
│   └── decoder.ts    # 核心解码器实现
├── dist/             # 编译输出
├── index.html        # 测试页面
├── package.json
└── tsconfig.json
```

## 下一步集成

这个模块是视频分段系统的第一个组件，后续可以与以下模块集成：

1. **WebGPU 特征提取器** - 处理 YUV 帧数据
2. **WASM PELT 算法** - 执行视频分段
3. **音频处理模块** - 语音识别和融合

## 开发命令

```bash
npm run build    # 编译 TypeScript
npm run dev      # 监听模式编译
npm run serve    # 启动静态服务器
npm start        # 编译并启动服务器
```
