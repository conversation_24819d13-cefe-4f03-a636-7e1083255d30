<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebCodecs 硬件解码器测试</title>

    <!-- Pyodide CDN -->
    <script src="https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .upload-btn:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.3s;
        }
        .results {
            margin-top: 20px;
        }
        .video-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .frame-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        .frame-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            text-align: center;
            background: white;
        }
        .frame-canvas {
            width: 100%;
            height: 100px;
            background: #f0f0f0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 WebCodecs 硬件解码器测试</h1>

        <!-- 任务管理区域 -->
        <div class="task-management" style="margin: 20px 0; padding: 15px; background: #e8f5e8; border-radius: 8px; border: 1px solid #c3e6c3;">
            <h4 style="margin: 0 0 15px 0; color: #2d5a2d;">📋 任务管理</h4>
            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
                <button id="createTaskBtn" class="upload-btn" style="background: #28a745; font-size: 14px; padding: 8px 16px;">
                    创建新任务
                </button>
                <div id="taskInfo" style="flex: 1; min-width: 200px;">
                    <span style="color: #666;">请先创建任务</span>
                </div>
                <button id="deleteTaskBtn" class="upload-btn" style="background: #dc3545; font-size: 14px; padding: 8px 16px; display: none;">
                    删除任务
                </button>
            </div>
        </div>

        <div class="upload-area" id="uploadArea">
            <p>拖拽视频文件到这里，或者点击选择文件</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                选择视频文件
            </button>
            <input type="file" id="fileInput" accept="video/*">
        </div>

        <div style="background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 10px 0; font-size: 14px; text-align: center;">
            💡 <strong>macOS用户提示：</strong> 为避免切换应用时的内存问题，请保持浏览器标签页可见直到处理完成
        </div>

        <!-- 特征提取配置 -->
        <div class="feature-controls" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <h4 style="margin: 0 0 15px 0; color: #495057;">🎯 特征提取配置</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <label>
                    特征提取器:
                    <select id="featureExtractorType" style="width: 100%; margin-top: 5px; padding: 5px;">
                        <option value="tensorflow">TensorFlow.js (GPU批处理)</option>
                        <option value="tensorflow-single">TensorFlow.js (GPU单帧)</option>
                        <option value="opencv">OpenCV.js (CPU)</option>
                    </select>
                </label>
                <label id="batchSizeLabel">
                    批大小:
                    <select id="batchSize" style="width: 100%; margin-top: 5px; padding: 5px;">
                        <option value="4">4帧</option>
                        <option value="8" selected>8帧</option>
                        <option value="16">16帧</option>
                        <option value="32">32帧</option>
                    </select>
                </label>
                <label>
                    目标FPS:
                    <select id="targetFps" style="width: 100%; margin-top: 5px; padding: 5px;">
                        <option value="">原始FPS</option>
                        <option value="1">1 FPS</option>
                        <option value="2">2 FPS</option>
                        <option value="5" selected>5 FPS</option>
                        <option value="10">10 FPS</option>
                        <option value="15">15 FPS</option>
                        <option value="24">24 FPS</option>
                        <option value="30">30 FPS</option>
                    </select>
                </label>
            </div>
        </div>

        <!-- 分段算法配置 -->
        <div class="algorithm-controls" style="margin: 20px 0; padding: 15px; background: #e3f2fd; border-radius: 8px;">
            <h4 style="margin: 0 0 15px 0; color: #1565c0;">🎬 分段算法配置</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px;">
                <label>
                    算法类型:
                    <select id="algorithmType" style="width: 100%; margin-top: 5px; padding: 5px;">
                        <option value="python">Python WASM</option>
                        <option value="go" selected>Go WASM</option>
                    </select>
                </label>
                <label>
                    最小平均段长(秒):
                    <input type="number" id="minAvgDuration" value="8" min="1" max="60" style="width: 100%; margin-top: 5px; padding: 5px;">
                </label>
                <label>
                    最大平均段长(秒):
                    <input type="number" id="maxAvgDuration" value="50" min="10" max="300" style="width: 100%; margin-top: 5px; padding: 5px;">
                </label>
                <label>
                    初始惩罚值:
                    <input type="number" id="initialPen" value="5" min="1" max="20" step="0.1" style="width: 100%; margin-top: 5px; padding: 5px;">
                </label>
                <label>
                    最大迭代次数:
                    <input type="number" id="maxIterations" value="20" min="5" max="50" style="width: 100%; margin-top: 5px; padding: 5px;">
                </label>
                <label>
                    最小段大小(帧数):
                    <input type="number" id="minSize" value="10" min="1" max="20" style="width: 100%; margin-top: 5px; padding: 5px;">
                </label>
            </div>
        </div>

        <!-- 融合算法配置 -->
        <div class="fusion-controls" style="margin: 20px 0; padding: 15px; background: #f0f8ff; border-radius: 8px;">
            <h4 style="margin: 0 0 15px 0; color: #4169e1;">🔄 音视频融合配置</h4>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <label>
                    最小分段时长(秒):
                    <input type="number" id="minSegmentDuration" value="1.0" min="0.5" max="10" step="0.5" style="width: 100%; margin-top: 5px; padding: 5px;">
                </label>
                <label>
                    对话保护边界(秒):
                    <input type="number" id="dialogueProtectionMargin" value="0.5" min="0.1" max="2.0" step="0.1" style="width: 100%; margin-top: 5px; padding: 5px;">
                </label>
            </div>
            <div style="margin-top: 10px; font-size: 12px; color: #666;">
                💡 融合算法会调整视觉分段边界，避免在对话中间切断，确保分段的语义完整性
            </div>
        </div>

        <div id="status" class="status" style="display: none;"></div>

        <!-- 恢复处理对话框 -->
        <div id="resumeDialog" style="display: none; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 10px 0;">
            <h4 style="margin: 0 0 10px 0; color: #856404;">🔄 检测到中断的处理</h4>
            <div style="margin: 0 0 15px 0; color: #856404;">
                <p style="margin: 0 0 8px 0;"><strong>⚠️ 注意：</strong>由于视频解码技术限制，无法从中断位置继续处理。</p>
                <p style="margin: 0;">选择"重新处理"将从头开始处理整个视频文件，之前的进度将丢失。</p>
            </div>
            <div style="text-align: center;">
                <button id="resumeBtn" style="background: #ffc107; color: #212529; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer; font-weight: bold;">重新处理</button>
                <button id="cancelResumeBtn" style="background: #6c757d; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">取消</button>
            </div>
        </div>

        <div class="progress" id="progressContainer" style="display: none;">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div class="results" id="results" style="display: none;">
            <h3>解码结果</h3>
            <div id="videoInfo" class="video-info"></div>
            <div id="framePreview" class="frame-preview"></div>

            <!-- 分段结果展示 -->
            <div id="segmentSection" style="display: none; margin-top: 20px;">
                <h3>🎬 视频分段结果</h3>
                <div id="segmentSummary" class="video-info"></div>
                <div id="segmentTimeline" style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                    <h4>分段时间点</h4>
                    <div id="segmentTimesList" style="font-family: monospace; line-height: 1.6;"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/mp4box@0.5.2/dist/mp4box.all.min.js"></script>
    <!-- TensorFlow.js (包含所有后端，包括WebGL) -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest/dist/tf.min.js"></script>
    <!-- 注意：已移除FFmpeg.wasm，现在使用浏览器原生API（Video + Canvas）进行帧提取 -->
    <script type="module">
        import { WebCodecsVideoDecoder } from './dist/decoder.js';
        import { VideoProcessingCoordinator } from './dist/coordinator.js';
        import { visibilityManager } from './dist/visibility-manager.js';
        import { TaskManager, taskManager } from './dist/task-manager.js';

        // 将可见性管理器暴露到全局，供coordinator使用
        window.visibilityManager = visibilityManager;

        // 添加可见性状态显示
        visibilityManager.addListener({
            onHidden: () => {
                showStatus('warning', '⏸️ 页面隐藏，视频处理已暂停以防止内存泄漏');
            },
            onVisible: () => {
                showStatus('info', '▶️ 页面恢复可见');
            }
        });

        let coordinator = null; // 视频处理协调器
        let featureExtractorType = 'tensorflow'; // 特征提取器类型
        let algorithmType = 'go'; // 算法类型
        let algorithmProcessor = null; // 算法处理器（预加载）

        // 任务管理相关变量
        let currentTaskId = null;

        // 检查 WebCodecs 支持
        function checkSupport() {
            if (!WebCodecsVideoDecoder.isSupported()) {
                showStatus('error', '❌ 当前浏览器不支持 WebCodecs API。请使用 Chrome 94+ 或 Edge 94+');
                return false;
            }
            showStatus('success', '✅ WebCodecs API 支持正常');
            return true;
        }

        // 显示状态信息
        function showStatus(type, message) {
            const statusEl = document.getElementById('status');
            statusEl.className = `status ${type}`;
            // 支持多行文本显示
            statusEl.innerHTML = message.replace(/\n/g, '<br>');
            statusEl.style.display = 'block';
        }

        // 更新进度
        function updateProgress(progress) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            progressContainer.style.display = 'block';
            progressBar.style.width = `${progress}%`;
        }

        // 显示视频信息
        function showVideoInfo(info) {
            const videoInfoEl = document.getElementById('videoInfo');
            videoInfoEl.innerHTML = `
                <h4>视频信息</h4>
                <p><strong>分辨率:</strong> ${info.width} × ${info.height}</p>
                <p><strong>时长:</strong> ${info.duration.toFixed(2)} 秒</p>
                <p><strong>帧率:</strong> ${info.frameRate.toFixed(2)} fps</p>
                <p><strong>编码:</strong> ${info.codec}</p>
            `;
        }

        // 冗余代码已移除 - 协调器负责所有帧处理
        // 协调器回调函数
        function onProgress(processedFrames, totalFrames) {
            const progress = totalFrames > 0 ? Math.round((processedFrames / totalFrames) * 100) : 0;
            showStatus('info', `🔄 处理进度: ${processedFrames}/${totalFrames} (${progress}%)`);
            updateProgress(progress);
        }

        function onComplete(stats) {
            let statusMessage = `✅ 处理完成！共处理 ${stats.totalFrames} 帧，总耗时 ${stats.totalTime.toFixed(2)}s`;
            statusMessage += `，平均每帧 ${stats.avgTimePerFrame.toFixed(1)}ms`;
            statusMessage += `\n🎯 解码耗时: ${stats.decodingTime.toFixed(2)}s`;
            statusMessage += `，特征提取耗时: ${stats.featureExtractionTime.toFixed(2)}s`;

            if (stats.audioProcessingTime) {
                statusMessage += `，音频处理耗时: ${stats.audioProcessingTime.toFixed(2)}s`;
            }

            if (stats.segmentTimes && stats.segmentTimes.length > 0) {
                statusMessage += `\n🎬 检测到 ${stats.segmentTimes.length} 个原始分段点`;
            }

            if (stats.fusionResult) {
                statusMessage += `\n🔄 融合处理完成，调整为 ${stats.fusionResult.totalSegments} 个分段`;
            }

            if (stats.asrResult) {
                statusMessage += `\n🎵 音频识别完成，识别到 ${stats.asrResult.sentences?.length || 0} 个句子`;
            }

            if (stats.segmentMetadata) {
                statusMessage += `\n📋 元数据构建完成`;
            }

            showStatus('success', statusMessage);

            // 直接以JSON格式展示所有结果
            setTimeout(() => {
                showAllResultsAsJSON(stats);
            }, 500);
            document.getElementById('progressContainer').style.display = 'none';
        }

        function showAllResultsAsJSON(stats) {
            const resultsContainer = document.getElementById('results');
            resultsContainer.style.display = 'block';

            // 创建JSON展示区域
            let htmlContent = `
                <h3>📊 处理结果 (JSON格式)</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">
                    <h4>任务信息</h4>
                    <pre style="background: #fff; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;">${JSON.stringify({
                        taskId: currentTaskId,
                        timestamp: new Date().toISOString()
                    }, null, 2)}</pre>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">
                    <h4>处理统计</h4>
                    <pre style="background: #fff; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;">${JSON.stringify({
                        totalFrames: stats.totalFrames,
                        totalTime: stats.totalTime,
                        avgTimePerFrame: stats.avgTimePerFrame,
                        decodingTime: stats.decodingTime,
                        featureExtractionTime: stats.featureExtractionTime,
                        audioProcessingTime: stats.audioProcessingTime
                    }, null, 2)}</pre>
                </div>
            `;

            // 如果有原始分段结果，单独展示
            if (stats.segmentTimes && stats.segmentTimes.length > 0) {
                htmlContent += `
                    <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;">
                        <h4>🎬 原始视觉分段结果</h4>
                        <pre style="background: #fff; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;">${JSON.stringify(stats.segmentTimes, null, 2)}</pre>
                    </div>
                `;
            }

            // 如果有ASR结果，单独展示
            if (stats.asrResult) {
                htmlContent += `
                    <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;">
                        <h4>🎵 音频识别结果</h4>
                        <pre style="background: #fff; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;">${JSON.stringify(stats.asrResult, null, 2)}</pre>
                    </div>
                `;
            }

            // 如果有融合结果，单独展示
            if (stats.fusionResult) {
                htmlContent += `
                    <div style="background: #e1f5fe; padding: 15px; border-radius: 5px; margin: 10px 0;">
                        <h4>🔄 音视频融合结果</h4>
                        <pre style="background: #fff; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;">${JSON.stringify(stats.fusionResult, null, 2)}</pre>
                    </div>
                `;
            }

            // 如果有分段元数据，单独展示
            if (stats.segmentMetadata) {
                htmlContent += `
                    <div style="background: #f3e5f5; padding: 15px; border-radius: 5px; margin: 10px 0;">
                        <h4>📋 分段元数据 (符合后端接口)</h4>
                        <pre style="background: #fff; padding: 10px; border-radius: 3px; overflow-x: auto; font-size: 12px;">${JSON.stringify(stats.segmentMetadata, null, 2)}</pre>
                    </div>
                `;
            }

            resultsContainer.innerHTML = htmlContent;
        }

        function onError(error) {
            showStatus('error', `❌ 处理错误: ${error.message}`);
            console.error('Processing error:', error);
        }

        function onResume(pausedState) {
            console.log('🔄 收到重新处理请求:', pausedState);
            showResumeDialog(pausedState);
        }

        // 音频处理回调函数
        function onAudioProgress(status) {
            console.log('🎵 音频处理进度:', status);
            showStatus('info', `🎵 ${status}`);
        }

        function onAudioComplete(result) {
            console.log('✅ 音频处理完成:', result);
            const sentenceCount = result.sentences?.length || 0;
            showStatus('success', `🎵 音频识别完成！识别到 ${sentenceCount} 个句子`);

            // 显示详细的音频识别结果
            if (sentenceCount > 0) {
                console.log('📝 识别到的句子:');
                result.sentences.forEach((sentence, index) => {
                    console.log(`句子 ${index + 1}: [${sentence.startTime.toFixed(2)}s - ${sentence.endTime.toFixed(2)}s] ${sentence.content}`);
                });
            }
        }

        function onAudioError(error) {
            console.error('❌ 音频处理失败:', error);
            showStatus('warning', `⚠️ 音频识别失败: ${error.message}，视频处理将继续进行`);
        }

        // 章节聚合回调函数
        function onChapterProgress(status) {
            console.log('📚 章节聚合进度:', status);
            showStatus('info', `📚 ${status}`);
        }

        function onChapterComplete(result) {
            console.log('✅ 章节聚合完成:', result);
            showStatus('success', `📚 章节聚合完成！状态: ${result.status}`);
        }

        function onChapterError(error) {
            console.error('❌ 章节聚合失败:', error);
            showStatus('warning', `⚠️ 章节聚合失败: ${error.message}，但视频处理已完成`);
        }

        function showResumeDialog(pausedState) {
            const dialog = document.getElementById('resumeDialog');
            dialog.style.display = 'block';

            // 重新处理按钮
            document.getElementById('resumeBtn').onclick = async () => {
                dialog.style.display = 'none';
                showStatus('info', '🔄 重新处理中（从头开始）...');
                try {
                    await coordinator.manualResume();
                } catch (error) {
                    onError(error);
                }
            };

            // 取消按钮
            document.getElementById('cancelResumeBtn').onclick = () => {
                dialog.style.display = 'none';
                coordinator.cancelResume();
                showStatus('info', '✅ 已取消重新处理，可以重新选择文件');
            };
        }

        // 显示分段结果
        function showSegmentResults(segmentTimes) {
            console.log('🎬 显示分段结果:', segmentTimes.length, '个分段点');

            const segmentSection = document.getElementById('segmentSection');
            const segmentSummary = document.getElementById('segmentSummary');
            const segmentTimesList = document.getElementById('segmentTimesList');

            // 显示摘要
            segmentSummary.innerHTML = `
                <h4>分段统计摘要</h4>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin: 10px 0;">
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <strong>� 分段数量</strong><br>
                        ${segmentTimes.length} 个分段点
                    </div>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
                        <strong>⏱️ 平均段长</strong><br>
                        ${segmentTimes.length > 0 ? (segmentTimes[segmentTimes.length - 1] / segmentTimes.length).toFixed(1) : 0} 秒
                    </div>
                </div>
            `;

            // 显示时间点列表
            if (segmentTimes.length > 0) {
                let timesList = '<strong>分段时间点：</strong><br>';
                segmentTimes.forEach((time, index) => {
                    const minutes = Math.floor(time / 60);
                    const seconds = (time % 60).toFixed(1);
                    timesList += `第 ${index + 1} 段: ${minutes}:${seconds.padStart(4, '0')}s<br>`;
                });
                segmentTimesList.innerHTML = timesList;
            } else {
                segmentTimesList.innerHTML = '<em>未检测到分段点</em>';
            }

            // 显示分段区域
            segmentSection.style.display = 'block';
        }

        // 显示处理结果
        function showProcessingResults() {
            const framePreviewContainer = document.getElementById('framePreview');
            framePreviewContainer.innerHTML = '<p>✅ 视频处理完成 - 所有帧已通过协调器处理</p>';
        }

        // 任务管理函数
        async function createTask() {
            try {
                showStatus('info', '正在创建任务...');

                const taskResponse = await taskManager.createVideoProcessingTask();
                currentTaskId = taskResponse.task_id;

                updateTaskInfo(taskResponse);
                showStatus('success', `任务创建成功: ${currentTaskId}`);

                // 启用文件上传
                document.getElementById('uploadArea').style.opacity = '1';
                document.getElementById('uploadArea').style.pointerEvents = 'auto';

            } catch (error) {
                console.error('创建任务失败:', error);
                showStatus('error', `创建任务失败: ${error.message}`);
            }
        }

        async function deleteTask() {
            if (!currentTaskId) return;

            try {
                showStatus('info', '正在删除任务...');
                await taskManager.deleteTask(currentTaskId);

                currentTaskId = null;
                updateTaskInfo(null);
                showStatus('success', '任务删除成功');

                // 禁用文件上传
                document.getElementById('uploadArea').style.opacity = '0.5';
                document.getElementById('uploadArea').style.pointerEvents = 'none';

            } catch (error) {
                console.error('删除任务失败:', error);
                showStatus('error', `删除任务失败: ${error.message}`);
            }
        }

        function updateTaskInfo(taskResponse) {
            const taskInfo = document.getElementById('taskInfo');
            const deleteBtn = document.getElementById('deleteTaskBtn');

            if (taskResponse) {
                taskInfo.innerHTML = `
                    <div style="font-size: 14px;">
                        <strong>任务ID:</strong> ${taskResponse.task_id}<br>
                        <strong>状态:</strong> ${taskResponse.status}<br>
                        <strong>创建时间:</strong> ${new Date(taskResponse.created_at).toLocaleString()}
                    </div>
                `;
                deleteBtn.style.display = 'inline-block';
            } else {
                taskInfo.innerHTML = '<span style="color: #666;">请先创建任务</span>';
                deleteBtn.style.display = 'none';
            }
        }

        // 处理文件上传
        async function handleFile(file) {
            if (!file.type.startsWith('video/')) {
                showStatus('error', '请选择视频文件');
                return;
            }

            // 检查是否有任务ID
            if (!currentTaskId) {
                showStatus('error', '请先创建任务');
                return;
            }

            document.getElementById('framePreview').innerHTML = '';
            document.getElementById('results').style.display = 'none';

            showStatus('info', '🔄 正在初始化解码器...');

            try {
                // 创建协调器
                coordinator = new VideoProcessingCoordinator({
                    onProgress: onProgress,
                    onComplete: onComplete,
                    onError: onError,
                    onResume: onResume,
                    onAudioProgress: onAudioProgress,
                    onAudioComplete: onAudioComplete,
                    onAudioError: onAudioError,
                    onChapterProgress: onChapterProgress,
                    onChapterComplete: onChapterComplete,
                    onChapterError: onChapterError
                });

                // 根据配置初始化特征提取器
                try {
                    const extractorType = featureExtractorType.startsWith('tensorflow') ? 'tensorflow' : featureExtractorType;
                    const useBatch = featureExtractorType === 'tensorflow'; // 批处理模式
                    const batchSize = parseInt(document.getElementById('batchSize').value) || 8;

                    await coordinator.initializeFeatureExtractor(extractorType, useBatch, batchSize);

                    let extractorName;
                    if (featureExtractorType === 'tensorflow') {
                        extractorName = `TensorFlow.js GPU (批处理, ${batchSize}帧/批)`;
                    } else if (featureExtractorType === 'tensorflow-single') {
                        extractorName = 'TensorFlow.js GPU (单帧)';
                    } else {
                        extractorName = 'OpenCV.js CPU';
                    }

                    showStatus('info', `🎯 ${extractorName} 特征提取器已启用`);
                } catch (error) {
                    console.warn('特征提取启用失败:', error);
                    showStatus('info', '⚠️ 特征提取不可用，继续普通解码');
                }

                showStatus('info', '🎬 开始处理视频...');
                document.getElementById('results').style.display = 'block';

                // 获取目标FPS
                const targetFpsValue = document.getElementById('targetFps').value;
                const targetFps = targetFpsValue ? parseInt(targetFpsValue) : undefined;

                // 设置算法配置
                const algorithmConfig = {
                    min_avg_duration: parseInt(document.getElementById('minAvgDuration').value) || 8,
                    max_avg_duration: parseInt(document.getElementById('maxAvgDuration').value) || 50,
                    initial_pen: parseFloat(document.getElementById('initialPen').value) || 5,
                    max_iterations: parseInt(document.getElementById('maxIterations').value) || 20,
                    min_size: parseInt(document.getElementById('minSize').value) || 10
                };
                coordinator.setAlgorithmConfig(algorithmConfig);
                coordinator.setAlgorithmType(algorithmType);

                // 设置融合配置（与Go版本完全一致）
                const fusionConfig = {
                    minSegmentDuration: parseFloat(document.getElementById('minSegmentDuration').value) || 1.0,
                    dialogueProtectionMargin: parseFloat(document.getElementById('dialogueProtectionMargin').value) || 0.5
                };
                coordinator.setFusionConfig(fusionConfig);
                console.log('🔄 融合配置已设置:', fusionConfig);

                // 使用协调器开始处理（协调器内部会创建和管理解码器）
                await coordinator.startProcessing(file, currentTaskId, targetFps);

            } catch (error) {
                showStatus('error', `❌ 初始化失败: ${error.message}`);
                console.error('Initialization error:', error);
            }
        }





        // 设置事件监听器
        document.addEventListener('DOMContentLoaded', async () => {
            if (!checkSupport()) return;

            // 预加载Go WASM模块（只加载模块，不初始化实例）
            console.log('🚀 开始预加载Go WASM模块...');
            try {
                const { getGoAlgorithmProcessor } = await import('./dist/algorithm-wasm-go.js');
                const goProcessor = getGoAlgorithmProcessor();
                await goProcessor.initialize(); // 预加载WASM模块
                console.log('✅ Go WASM模块预加载完成');
            } catch (error) {
                console.warn('⚠️ Go WASM模块预加载失败:', error);
            }

            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');

            // 任务管理按钮事件
            document.getElementById('createTaskBtn').addEventListener('click', createTask);
            document.getElementById('deleteTaskBtn').addEventListener('click', deleteTask);

            // 初始化时禁用文件上传
            uploadArea.style.opacity = '0.5';
            uploadArea.style.pointerEvents = 'none';

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFile(e.target.files[0]);
                }
            });

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                if (e.dataTransfer.files.length > 0) {
                    handleFile(e.dataTransfer.files[0]);
                }
            });

            // 特征提取器配置事件
            document.getElementById('featureExtractorType').addEventListener('change', (e) => {
                featureExtractorType = e.target.value;
                console.log('特征提取器类型:', featureExtractorType);

                // 只有批处理模式才显示批大小选项
                const batchSizeLabel = document.getElementById('batchSizeLabel');
                if (featureExtractorType === 'tensorflow') {
                    batchSizeLabel.style.display = 'block';
                } else {
                    batchSizeLabel.style.display = 'none';
                }
            });

            // 算法类型配置事件
            document.getElementById('algorithmType').addEventListener('change', (e) => {
                algorithmType = e.target.value;
                console.log('算法类型:', algorithmType);
            });

            // 初始化批大小显示状态
            const initialExtractorType = document.getElementById('featureExtractorType').value;
            const batchSizeLabel = document.getElementById('batchSizeLabel');
            if (initialExtractorType === 'tensorflow') {
                batchSizeLabel.style.display = 'block';
            } else {
                batchSizeLabel.style.display = 'none';
            }

        });

        // 清理资源
        window.addEventListener('beforeunload', () => {
            if (coordinator) {
                coordinator.dispose();
            }
        });
    </script>
</body>
</html>
