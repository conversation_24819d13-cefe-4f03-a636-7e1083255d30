"""
视频分段算法模块
"""
import numpy as np
import ruptures as rpt
from typing import List, Tuple, Dict, Any
import json


class SegmentProcessor:
    """视频分段处理器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        if config is None:
            config = {}
            
        self.min_avg_duration = config.get("min_avg_duration", 8)
        self.max_avg_duration = config.get("max_avg_duration", 50)
        self.initial_pen = config.get("initial_pen", 5)
        self.max_iterations = config.get("max_iterations", 20)
        self.min_size = config.get("min_size", 2)
        
    def normalize_and_combine(self, features: List[List[float]]) -> List[float]:
        """归一化并组合特征差异"""
        if not features:
            return []
            
        features_array = np.array(features)
        mean_vals = np.mean(features_array, axis=0)
        std_vals = np.std(features_array, axis=0)
        std_vals = np.where(std_vals == 0, 1e-10, std_vals)
        normalized = (features_array - mean_vals) / std_vals
        combined = np.sum(normalized, axis=1)
        return combined.tolist()
    
    def detect_segments_with_auto_penalty(
        self, 
        combined_scores: List[float], 
        timestamps: List[float],
        target_fps: float = 5.0
    ) -> Tuple[List[float], Dict[str, Any]]:
        """使用自动调整惩罚参数的算法检测分段"""
        if len(combined_scores) == 0:
            return [timestamps[0], timestamps[-1]], {"iterations": 0, "final_pen": self.initial_pen}
            
        min_size = max(self.min_size, int(target_fps / 2))
        pen = self.initial_pen
        best_segments = []
        debug_info = {
            "iterations": 0,
            "final_pen": pen,
            "avg_durations": [],
            "segment_counts": []
        }
        
        for iteration in range(self.max_iterations):
            debug_info["iterations"] = iteration + 1
            
            segments = self._detect_segments_core(
                combined_scores, 
                timestamps, 
                min_size, 
                pen
            )
            
            if len(segments) > 1:
                durations = [segments[i+1] - segments[i] for i in range(len(segments)-1)]
                avg_duration = np.mean(durations) if durations else 0
            else:
                avg_duration = timestamps[-1] - timestamps[0]
                
            debug_info["avg_durations"].append(avg_duration)
            debug_info["segment_counts"].append(len(segments) - 1)
            
            if self.min_avg_duration <= avg_duration <= self.max_avg_duration:
                best_segments = segments
                debug_info["final_pen"] = pen
                break
            elif avg_duration < self.min_avg_duration:
                pen *= 1.5
                best_segments = segments
            else:
                pen *= 0.7
                best_segments = segments
                
        if not best_segments:
            best_segments = self._detect_segments_core(
                combined_scores, 
                timestamps, 
                min_size, 
                self.initial_pen
            )
            
        debug_info["final_pen"] = pen
        return best_segments, debug_info
    
    def _detect_segments_core(
        self,
        combined_scores: List[float],
        timestamps: List[float],
        min_size: int,
        pen: float
    ) -> List[float]:
        """核心分段检测算法"""
        if len(combined_scores) == 0:
            return [timestamps[0], timestamps[-1]]
            
        try:
            scores_array = np.array(combined_scores).reshape(-1, 1)
            algo = rpt.Pelt(model="l2", min_size=min_size).fit(scores_array)
            breakpoints = algo.predict(pen=pen)
            
            if not breakpoints or breakpoints == [len(combined_scores)]:
                return [timestamps[0], timestamps[-1]]
            
            valid_breakpoints = []
            for bp in breakpoints:
                if 0 < bp < len(timestamps):
                    valid_breakpoints.append(bp)
                    
            valid_breakpoints = sorted(set(valid_breakpoints))
            
            segments = [timestamps[0]]
            segments.extend(timestamps[bp] for bp in valid_breakpoints)
            
            if segments[-1] != timestamps[-1]:
                segments.append(timestamps[-1])
                
            return segments
            
        except Exception as e:
            print(f"分段算法执行失败: {e}")
            return [timestamps[0], timestamps[-1]]


def process_features_to_segments(
    features_json: str, 
    timestamps_json: str, 
    config_json: str = "{}"
) -> str:
    """WebAssembly调用的主要接口"""
    try:
        features = json.loads(features_json)
        timestamps = json.loads(timestamps_json)
        config = json.loads(config_json)
        
        processor = SegmentProcessor(config)
        combined_scores = processor.normalize_and_combine(features)
        segments, debug_info = processor.detect_segments_with_auto_penalty(
            combined_scores, 
            timestamps,
            config.get("target_fps", 5.0)
        )
        
        result = {
            "segments": segments,
            "debug_info": debug_info,
            "combined_scores": combined_scores,
            "success": True
        }
        
        return json.dumps(result)
        
    except Exception as e:
        error_result = {
            "segments": [],
            "debug_info": {},
            "combined_scores": [],
            "success": False,
            "error": str(e)
        }
        return json.dumps(error_result)
