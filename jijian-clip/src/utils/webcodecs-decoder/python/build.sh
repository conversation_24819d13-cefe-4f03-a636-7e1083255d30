#!/bin/bash

# 编译算法模块为 WASM
set -e

echo "🔧 编译算法模块..."

# 创建虚拟环境
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install pyodide-build numpy ruptures

# 创建 setup.py
cat > setup.py << 'EOF'
from setuptools import setup

setup(
    name="segment",
    version="1.0.0",
    py_modules=["algorithm"],
    install_requires=["numpy", "ruptures"]
)
EOF

# 编译
pyodide build

# 移动结果
mkdir -p ../dist
mv dist/*.whl ../dist/segment.whl

echo "✅ 算法模块编译完成: ../dist/segment.whl"
