/**
 * H.265/HEVC 兼容性检测和处理工具
 * 专门用于检测和处理H.265格式视频的兼容性问题
 */

export interface H265CompatibilityResult {
  isH265: boolean;
  isSupported: boolean;
  browserSupport: {
    webCodecsSupport: boolean;
    hardwareDecoding: boolean;
    softwareDecoding: boolean;
  };
  issues: string[];
  recommendations: string[];
  conversionCommands: string[];
}

export interface VideoCodecInfo {
  codec: string;
  profile?: string;
  level?: string;
  isH265: boolean;
  formatName: string;
}

/**
 * 检测视频编码格式是否为H.265/HEVC
 */
export function detectH265Codec(codec: string): VideoCodecInfo {
  const normalizedCodec = codec.toLowerCase();
  
  const isH265 = (
    normalizedCodec.startsWith('hvc1') ||
    normalizedCodec.startsWith('hev1') ||
    normalizedCodec.includes('hevc') ||
    normalizedCodec.includes('h265') ||
    normalizedCodec.includes('h.265')
  );

  let formatName = 'Unknown';
  let profile = undefined;
  let level = undefined;

  if (isH265) {
    formatName = 'H.265/HEVC';
    
    // 尝试解析profile和level信息
    if (codec.startsWith('hvc1') || codec.startsWith('hev1')) {
      const parts = codec.split('.');
      if (parts.length >= 2) {
        profile = parts[1];
      }
      if (parts.length >= 3) {
        level = parts[2];
      }
    }
  } else if (normalizedCodec.startsWith('avc1')) {
    formatName = 'H.264/AVC';
  } else if (normalizedCodec.startsWith('vp8')) {
    formatName = 'VP8';
  } else if (normalizedCodec.startsWith('vp9')) {
    formatName = 'VP9';
  }

  return {
    codec,
    profile,
    level,
    isH265,
    formatName
  };
}

/**
 * 检查浏览器对H.265的支持情况
 */
export async function checkH265BrowserSupport(): Promise<H265CompatibilityResult['browserSupport']> {
  const result = {
    webCodecsSupport: false,
    hardwareDecoding: false,
    softwareDecoding: false
  };

  try {
    // 检查WebCodecs API是否可用
    if (typeof VideoDecoder === 'undefined') {
      console.warn('⚠️ WebCodecs API 不可用');
      return result;
    }

    // 测试常见的H.265编码配置
    const h265Configs = [
      'hvc1.1.6.L93.B0',  // Main Profile, Level 3.1
      'hev1.1.6.L93.B0',  // Main Profile, Level 3.1
      'hvc1.2.4.L93.B0',  // Main 10 Profile
      'hev1.2.4.L93.B0'   // Main 10 Profile
    ];

    for (const codec of h265Configs) {
      try {
        const config = { codec };
        const support = await VideoDecoder.isConfigSupported(config);
        
        if (support.supported) {
          result.webCodecsSupport = true;
          
          // 尝试检测是否为硬件解码（这个检测不是100%准确）
          if (support.config && support.config.hardwareAcceleration !== 'no-preference') {
            result.hardwareDecoding = true;
          } else {
            result.softwareDecoding = true;
          }
          
          console.log(`✅ H.265编码 ${codec} 受支持`);
          break;
        }
      } catch (error) {
        console.warn(`⚠️ H.265编码 ${codec} 检测失败:`, error);
      }
    }

  } catch (error) {
    console.error('❌ H.265支持检测失败:', error);
  }

  return result;
}

/**
 * 生成H.265转换建议
 */
export function generateH265ConversionRecommendations(codecInfo: VideoCodecInfo): string[] {
  const recommendations = [];

  if (codecInfo.isH265) {
    recommendations.push(
      '🎯 建议转换为H.264格式以获得最佳兼容性',
      '📱 H.264格式在所有现代浏览器中都有良好支持',
      '⚡ H.264解码性能更好，功耗更低',
      '🔧 可以使用FFmpeg或其他视频转换工具进行转换'
    );
  }

  return recommendations;
}

/**
 * 生成FFmpeg转换命令
 */
export function generateConversionCommands(inputFile: string = 'input.mp4', outputFile: string = 'output.mp4'): string[] {
  return [
    // 基本转换（保持原始质量）
    `ffmpeg -i ${inputFile} -c:v libx264 -c:a copy ${outputFile}`,
    
    // 高质量转换
    `ffmpeg -i ${inputFile} -c:v libx264 -preset slow -crf 18 -c:a copy ${outputFile}`,
    
    // 快速转换（适合测试）
    `ffmpeg -i ${inputFile} -c:v libx264 -preset fast -crf 23 -c:a copy ${outputFile}`,
    
    // 压缩转换（减小文件大小）
    `ffmpeg -i ${inputFile} -c:v libx264 -preset medium -crf 28 -c:a aac -b:a 128k ${outputFile}`,
    
    // Web优化转换
    `ffmpeg -i ${inputFile} -c:v libx264 -preset fast -crf 23 -movflags +faststart -c:a aac -b:a 128k ${outputFile}`
  ];
}

/**
 * 完整的H.265兼容性检查
 */
export async function checkH265Compatibility(codec: string): Promise<H265CompatibilityResult> {
  const codecInfo = detectH265Codec(codec);
  const browserSupport = await checkH265BrowserSupport();
  
  const issues: string[] = [];
  const recommendations: string[] = [];

  if (codecInfo.isH265) {
    if (!browserSupport.webCodecsSupport) {
      issues.push('当前浏览器不支持H.265/HEVC解码');
      issues.push('WebCodecs API中H.265支持有限');
      
      recommendations.push('转换为H.264格式以获得最佳兼容性');
      recommendations.push('使用支持H.265的浏览器（如较新版本的Chrome/Edge）');
    } else {
      if (!browserSupport.hardwareDecoding) {
        issues.push('可能只支持软件解码，性能较差');
        recommendations.push('考虑转换为H.264以获得更好的性能');
      }
    }
    
    recommendations.push(...generateH265ConversionRecommendations(codecInfo));
  }

  const conversionCommands = codecInfo.isH265 ? generateConversionCommands() : [];

  return {
    isH265: codecInfo.isH265,
    isSupported: browserSupport.webCodecsSupport,
    browserSupport,
    issues,
    recommendations,
    conversionCommands
  };
}

/**
 * 显示H.265兼容性报告
 */
export function displayH265CompatibilityReport(result: H265CompatibilityResult): void {
  console.group('🎬 H.265兼容性检查报告');
  
  console.log('📊 基本信息:');
  console.log(`  - 是否为H.265格式: ${result.isH265 ? '是' : '否'}`);
  console.log(`  - 浏览器支持: ${result.isSupported ? '支持' : '不支持'}`);
  
  if (result.isH265) {
    console.log('🔧 浏览器支持详情:');
    console.log(`  - WebCodecs支持: ${result.browserSupport.webCodecsSupport ? '是' : '否'}`);
    console.log(`  - 硬件解码: ${result.browserSupport.hardwareDecoding ? '是' : '否'}`);
    console.log(`  - 软件解码: ${result.browserSupport.softwareDecoding ? '是' : '否'}`);
  }
  
  if (result.issues.length > 0) {
    console.log('⚠️ 发现的问题:');
    result.issues.forEach(issue => console.log(`  - ${issue}`));
  }
  
  if (result.recommendations.length > 0) {
    console.log('💡 建议:');
    result.recommendations.forEach(rec => console.log(`  - ${rec}`));
  }
  
  if (result.conversionCommands.length > 0) {
    console.log('🔧 转换命令示例:');
    result.conversionCommands.forEach((cmd, index) => {
      console.log(`  ${index + 1}. ${cmd}`);
    });
  }
  
  console.groupEnd();
}
