/**
 * 🎯 动画性能管理器
 * 控制同时运行的动画数量，避免性能问题
 */

interface AnimationConfig {
  element: HTMLElement
  animationName: string
  duration: number
  priority: 'high' | 'medium' | 'low'
  callback?: () => void
}

interface AnimationQueue {
  id: string
  config: AnimationConfig
  startTime?: number
}

class AnimationManager {
  private static instance: AnimationManager
  private runningAnimations = new Map<string, AnimationQueue>()
  private pendingAnimations: AnimationQueue[] = []
  private maxConcurrentAnimations = 8 // 最大同时运行动画数
  private animationCounter = 0

  // 性能监控
  private performanceMonitor = {
    fps: 60,
    lastFrameTime: 0,
    frameCount: 0,
    isMonitoring: false
  }

  private constructor() {
    this.startPerformanceMonitoring()
    this.adjustMaxAnimationsBasedOnDevice()
  }

  static getInstance(): AnimationManager {
    if (!AnimationManager.instance) {
      AnimationManager.instance = new AnimationManager()
    }
    return AnimationManager.instance
  }

  /**
   * 根据设备性能调整最大动画数量
   */
  private adjustMaxAnimationsBasedOnDevice() {
    // 检测设备性能
    const isLowEndDevice = this.detectLowEndDevice()
    const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    
    if (isLowEndDevice) {
      this.maxConcurrentAnimations = 4
    } else if (isMobile) {
      this.maxConcurrentAnimations = 6
    } else {
      this.maxConcurrentAnimations = 8
    }

    console.log(`🎯 动画管理器：最大并发动画数设置为 ${this.maxConcurrentAnimations}`)
  }

  /**
   * 检测低端设备
   */
  private detectLowEndDevice(): boolean {
    // 检查硬件并发数
    const cores = navigator.hardwareConcurrency || 4
    
    // 检查内存（如果可用）
    const memory = (navigator as any).deviceMemory || 4
    
    // 检查连接类型
    const connection = (navigator as any).connection
    const isSlowConnection = connection && (
      connection.effectiveType === 'slow-2g' || 
      connection.effectiveType === '2g' ||
      connection.effectiveType === '3g'
    )

    return cores <= 2 || memory <= 2 || isSlowConnection
  }

  /**
   * 启动性能监控
   */
  private startPerformanceMonitoring() {
    if (this.performanceMonitor.isMonitoring) return

    this.performanceMonitor.isMonitoring = true
    this.performanceMonitor.lastFrameTime = performance.now()

    const monitorFrame = (currentTime: number) => {
      this.performanceMonitor.frameCount++
      
      const deltaTime = currentTime - this.performanceMonitor.lastFrameTime
      if (deltaTime >= 1000) { // 每秒计算一次FPS
        this.performanceMonitor.fps = Math.round(
          (this.performanceMonitor.frameCount * 1000) / deltaTime
        )
        this.performanceMonitor.frameCount = 0
        this.performanceMonitor.lastFrameTime = currentTime

        // 根据FPS动态调整动画数量
        this.adjustAnimationsBasedOnFPS()
      }

      if (this.performanceMonitor.isMonitoring) {
        requestAnimationFrame(monitorFrame)
      }
    }

    requestAnimationFrame(monitorFrame)
  }

  /**
   * 根据FPS动态调整动画数量
   */
  private adjustAnimationsBasedOnFPS() {
    const currentFPS = this.performanceMonitor.fps
    
    if (currentFPS < 30 && this.maxConcurrentAnimations > 2) {
      // FPS过低，减少动画数量
      this.maxConcurrentAnimations = Math.max(2, this.maxConcurrentAnimations - 1)
      console.warn(`⚠️ FPS过低(${currentFPS})，减少并发动画数至 ${this.maxConcurrentAnimations}`)
      this.pauseLowPriorityAnimations()
    } else if (currentFPS > 50 && this.maxConcurrentAnimations < 8) {
      // FPS良好，可以增加动画数量
      this.maxConcurrentAnimations = Math.min(8, this.maxConcurrentAnimations + 1)
      console.log(`✅ FPS良好(${currentFPS})，增加并发动画数至 ${this.maxConcurrentAnimations}`)
      this.processQueue()
    }
  }

  /**
   * 暂停低优先级动画
   */
  private pauseLowPriorityAnimations() {
    const lowPriorityAnimations = Array.from(this.runningAnimations.entries())
      .filter(([_, animation]) => animation.config.priority === 'low')
      .slice(0, Math.max(0, this.runningAnimations.size - this.maxConcurrentAnimations))

    lowPriorityAnimations.forEach(([id, animation]) => {
      this.stopAnimation(id)
      // 重新加入队列，但优先级降低
      this.pendingAnimations.unshift(animation)
    })
  }

  /**
   * 添加动画到队列
   */
  addAnimation(config: AnimationConfig): string {
    const id = `anim_${++this.animationCounter}_${Date.now()}`
    const animationQueue: AnimationQueue = { id, config }

    // 高优先级动画直接尝试运行
    if (config.priority === 'high') {
      this.pendingAnimations.unshift(animationQueue)
    } else {
      this.pendingAnimations.push(animationQueue)
    }

    this.processQueue()
    return id
  }

  /**
   * 处理动画队列
   */
  private processQueue() {
    while (
      this.pendingAnimations.length > 0 && 
      this.runningAnimations.size < this.maxConcurrentAnimations
    ) {
      const animation = this.pendingAnimations.shift()!
      this.startAnimation(animation)
    }
  }

  /**
   * 启动动画
   */
  private startAnimation(animation: AnimationQueue) {
    const { id, config } = animation

    try {
      // 设置GPU加速属性
      config.element.style.willChange = 'transform, opacity'
      config.element.style.backfaceVisibility = 'hidden'

      // 🎯 修复：正确处理包含空格的CSS类名
      const classNames = config.animationName.split(' ').filter(name => name.trim())
      classNames.forEach(className => {
        config.element.classList.add(className)
      })

      animation.startTime = performance.now()
      this.runningAnimations.set(id, animation)

      // 设置动画完成回调
      const handleAnimationEnd = () => {
        this.stopAnimation(id)
        config.callback?.()
        config.element.removeEventListener('animationend', handleAnimationEnd)
        config.element.removeEventListener('transitionend', handleAnimationEnd)
      }

      config.element.addEventListener('animationend', handleAnimationEnd)
      config.element.addEventListener('transitionend', handleAnimationEnd)

      // 备用超时清理
      setTimeout(() => {
        if (this.runningAnimations.has(id)) {
          this.stopAnimation(id)
          config.callback?.()
        }
      }, config.duration + 100)

      console.log(`🎬 启动动画: ${id} (${config.animationName}), 当前运行: ${this.runningAnimations.size}`)
      
    } catch (error) {
      console.error(`❌ 动画启动失败: ${id}`, error)
      this.stopAnimation(id)
    }
  }

  /**
   * 停止动画
   */
  stopAnimation(id: string) {
    const animation = this.runningAnimations.get(id)
    if (!animation) return

    try {
      // 🎯 修复：正确处理包含空格的CSS类名
      const classNames = animation.config.animationName.split(' ').filter(name => name.trim())
      classNames.forEach(className => {
        animation.config.element.classList.remove(className)
      })

      // 清理GPU加速属性
      animation.config.element.style.willChange = 'auto'

      this.runningAnimations.delete(id)

      // 处理队列中的下一个动画
      this.processQueue()

      console.log(`🛑 停止动画: ${id}, 剩余运行: ${this.runningAnimations.size}`)

    } catch (error) {
      console.error(`❌ 动画停止失败: ${id}`, error)
    }
  }

  /**
   * 获取当前性能状态
   */
  getPerformanceStatus() {
    return {
      fps: this.performanceMonitor.fps,
      runningAnimations: this.runningAnimations.size,
      pendingAnimations: this.pendingAnimations.length,
      maxConcurrentAnimations: this.maxConcurrentAnimations
    }
  }

  /**
   * 清理所有动画
   */
  clearAllAnimations() {
    this.runningAnimations.forEach((_, id) => this.stopAnimation(id))
    this.pendingAnimations = []
  }

  /**
   * 停止性能监控
   */
  stopPerformanceMonitoring() {
    this.performanceMonitor.isMonitoring = false
  }
}

// 导出单例实例
export const animationManager = AnimationManager.getInstance()

// 便捷函数
export const addAnimation = (config: AnimationConfig) => animationManager.addAnimation(config)
export const stopAnimation = (id: string) => animationManager.stopAnimation(id)
export const getPerformanceStatus = () => animationManager.getPerformanceStatus()
