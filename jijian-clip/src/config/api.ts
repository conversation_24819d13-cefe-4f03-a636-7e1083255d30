/**
 * API 配置文件
 * 统一管理所有后端 API 地址和配置
 */

// 环境配置
const isDevelopment = import.meta.env.DEV;
const isProduction = import.meta.env.PROD;

// 默认 API 配置
const DEFAULT_CONFIG = {
  // 开发环境配置
  development: {
    // 主要后端服务（AI 聊天、指令处理等）
    mainApiUrl: 'http://localhost:8000',
    
    // WebCodecs 视频处理服务
    videoProcessingApiUrl: 'https://localhost:60850',
    
    // 文件上传服务
    uploadApiUrl: 'http://localhost:8000',
  },
  
  // 生产环境配置
  production: {
    // 生产环境 API 地址（需要根据实际部署调整）
    mainApiUrl: 'https://your-api-domain.com',
    videoProcessingApiUrl: 'https://your-video-api-domain.com',
    uploadApiUrl: 'https://your-upload-api-domain.com',
  }
};

// 当前环境配置
const currentConfig = isDevelopment ? DEFAULT_CONFIG.development : DEFAULT_CONFIG.production;

// API 端点配置
export const API_CONFIG = {
  // 基础 URL
  BASE_URL: currentConfig.mainApiUrl,
  VIDEO_PROCESSING_BASE_URL: currentConfig.videoProcessingApiUrl,
  UPLOAD_BASE_URL: currentConfig.uploadApiUrl,
  
  // API 版本
  API_VERSION: 'v1',
  
  // 完整的 API 路径
  ENDPOINTS: {
    // 主要 API 端点
    HEALTH: `${currentConfig.mainApiUrl}/api/v1/health`,
    AI_CHAT: `${currentConfig.mainApiUrl}/api/v1/ai/chat`,
    
    // 文件上传端点
    UPLOAD_VIDEO: `${currentConfig.uploadApiUrl}/api/v1/upload/video`,
    
    // WebCodecs 视频处理端点
    TASK: {
      CREATE: `${currentConfig.videoProcessingApiUrl}/api/v1/task/create`,
      STATUS: `${currentConfig.videoProcessingApiUrl}/api/v1/task/status`,
      DELETE: `${currentConfig.videoProcessingApiUrl}/api/v1/task/delete`,
    },
    
    // 音频处理端点
    AUDIO: {
      ASR: `${currentConfig.videoProcessingApiUrl}/api/v1/asr/audio-recognition`,
    },
    
    // 分段处理端点
    SEGMENTS: {
      UPLOAD_METADATA: `${currentConfig.videoProcessingApiUrl}/api/v1/segments/upload-metadata`,
      UPLOAD_FRAMES: `${currentConfig.videoProcessingApiUrl}/api/v1/segments/upload-frames`,
    },
    
    // 章节聚合端点
    CHAPTERS: {
      AGGREGATE: `${currentConfig.videoProcessingApiUrl}/api/v1/chapters/aggregate`,
    }
  },
  
  // 请求配置
  REQUEST_CONFIG: {
    timeout: 30000, // 30秒超时
    headers: {
      'Content-Type': 'application/json',
    }
  },
  
  // WebCodecs 特定配置
  WEBCODECS_CONFIG: {
    jpegQuality: 80, // JPEG 质量 (1-100)
    targetFps: 5, // 默认目标帧率
    minFrameCount: 4, // 最小帧数
    maxFrameCount: 512, // 最大帧数
  }
};

// 环境变量覆盖配置
if (import.meta.env.VITE_API_BASE_URL) {
  API_CONFIG.BASE_URL = import.meta.env.VITE_API_BASE_URL;
}

if (import.meta.env.VITE_VIDEO_API_BASE_URL) {
  API_CONFIG.VIDEO_PROCESSING_BASE_URL = import.meta.env.VITE_VIDEO_API_BASE_URL;
}

if (import.meta.env.VITE_UPLOAD_API_BASE_URL) {
  API_CONFIG.UPLOAD_BASE_URL = import.meta.env.VITE_UPLOAD_API_BASE_URL;
}

// 导出配置验证函数
export function validateApiConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 检查必要的 URL 配置
  if (!API_CONFIG.BASE_URL) {
    errors.push('主 API 地址未配置');
  }
  
  if (!API_CONFIG.VIDEO_PROCESSING_BASE_URL) {
    errors.push('视频处理 API 地址未配置');
  }
  
  if (!API_CONFIG.UPLOAD_BASE_URL) {
    errors.push('文件上传 API 地址未配置');
  }
  
  // 检查 URL 格式
  const urlPattern = /^https?:\/\/.+/;
  if (!urlPattern.test(API_CONFIG.BASE_URL)) {
    errors.push('主 API 地址格式不正确');
  }
  
  if (!urlPattern.test(API_CONFIG.VIDEO_PROCESSING_BASE_URL)) {
    errors.push('视频处理 API 地址格式不正确');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

// 导出便捷函数
export function getApiUrl(endpoint: string): string {
  return `${API_CONFIG.BASE_URL}/api/${API_CONFIG.API_VERSION}${endpoint}`;
}

export function getVideoProcessingApiUrl(endpoint: string): string {
  return `${API_CONFIG.VIDEO_PROCESSING_BASE_URL}/api/${API_CONFIG.API_VERSION}${endpoint}`;
}

export function getUploadApiUrl(endpoint: string): string {
  return `${API_CONFIG.UPLOAD_BASE_URL}/api/${API_CONFIG.API_VERSION}${endpoint}`;
}

// 打印当前配置（仅开发环境）
if (isDevelopment) {
  console.log('🔧 API 配置:', {
    environment: 'development',
    baseUrl: API_CONFIG.BASE_URL,
    videoProcessingUrl: API_CONFIG.VIDEO_PROCESSING_BASE_URL,
    uploadUrl: API_CONFIG.UPLOAD_BASE_URL
  });
  
  const validation = validateApiConfig();
  if (!validation.valid) {
    console.warn('⚠️ API 配置验证失败:', validation.errors);
  }
}
