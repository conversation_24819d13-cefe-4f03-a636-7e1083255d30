/**
 * 后端服务配置管理
 * 统一管理所有后端服务的地址和配置
 */

// 后端服务配置接口
export interface BackendConfig {
  // AI 后端服务（WebSocket）
  aiBackendUrl: string;
  aiBackendEnabled: boolean;
  
  // 任务管理后端服务（HTTP）
  taskBackendUrl: string;
  taskBackendEnabled: boolean;
  
  // 其他配置
  timeout: number;
  retryAttempts: number;
}

// 默认配置
const DEFAULT_CONFIG: BackendConfig = {
  aiBackendUrl: 'ws://localhost:8000',
  aiBackendEnabled: false,
  taskBackendUrl: 'http://localhost:60850',
  taskBackendEnabled: false,
  timeout: 5000,
  retryAttempts: 3
};

// 从环境变量获取配置
export function getBackendConfig(): BackendConfig {
  const config: BackendConfig = {
    // AI 后端配置
    aiBackendUrl: import.meta.env.VITE_AI_BACKEND_URL || DEFAULT_CONFIG.aiBackendUrl,
    aiBackendEnabled: import.meta.env.VITE_AI_BACKEND_ENABLED === 'true',
    
    // 任务后端配置
    taskBackendUrl: import.meta.env.VITE_TASK_BACKEND_URL || DEFAULT_CONFIG.taskBackendUrl,
    taskBackendEnabled: import.meta.env.VITE_TASK_BACKEND_ENABLED === 'true',
    
    // 其他配置
    timeout: parseInt(import.meta.env.VITE_BACKEND_TIMEOUT || '5000'),
    retryAttempts: parseInt(import.meta.env.VITE_BACKEND_RETRY_ATTEMPTS || '3')
  };
  
  console.log('🔧 后端服务配置:', {
    ...config,
    env: import.meta.env.MODE,
    isDev: import.meta.env.DEV
  });
  
  return config;
}

// 获取任务后端配置（向后兼容）
export function getTaskBackendConfig() {
  const config = getBackendConfig();
  return {
    backendUrl: config.taskBackendUrl,
    backendEnabled: config.taskBackendEnabled
  };
}

// 获取 AI 后端配置
export function getAIBackendConfig() {
  const config = getBackendConfig();
  return {
    backendUrl: config.aiBackendUrl,
    backendEnabled: config.aiBackendEnabled
  };
}

// 检查服务可用性的通用方法
export async function checkServiceHealth(
  url: string,
  healthPath: string = '/health',
  timeout: number = 3000
): Promise<boolean> {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const fullUrl = `${url}${healthPath}`;
    console.log(`🌐 健康检查请求: ${fullUrl}`);

    const response = await fetch(fullUrl, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    clearTimeout(timeoutId);

    console.log(`📡 健康检查响应: ${fullUrl} -> ${response.status} ${response.statusText}`);

    return response.ok;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.warn(`⚠️ 服务健康检查失败 ${url}${healthPath}: ${errorMessage}`);
    return false;
  }
}

// 任务后端健康检查
export async function checkTaskBackendHealth(): Promise<boolean> {
  const config = getBackendConfig();

  if (!config.taskBackendEnabled) {
    console.log('📝 任务后端已在环境变量中禁用');
    return false;
  }

  // 尝试多个可能的健康检查路径
  const healthPaths = [
    '/api/v1/health',
    '/health',
    '/api/health',
    '/ping',
    '/status'
  ];

  console.log('🔍 尝试多个健康检查路径...');

  for (const path of healthPaths) {
    console.log(`🌐 尝试路径: ${config.taskBackendUrl}${path}`);
    const isHealthy = await checkServiceHealth(config.taskBackendUrl, path, config.timeout);

    if (isHealthy) {
      console.log(`✅ 健康检查成功，使用路径: ${path}`);
      return true;
    }
  }

  console.warn('⚠️ 所有健康检查路径都失败，但仍然尝试创建任务');

  // 如果健康检查都失败，但用户明确启用了后端，我们仍然尝试创建任务
  // 因为有些后端可能没有健康检查接口，但创建任务接口是可用的
  return true; // 改为 true，让它尝试创建任务
}

// AI 后端健康检查
export async function checkAIBackendHealth(): Promise<boolean> {
  const config = getBackendConfig();
  
  if (!config.aiBackendEnabled) {
    console.log('🤖 AI 后端已在环境变量中禁用');
    return false;
  }
  
  // WebSocket 连接检查需要特殊处理
  return new Promise((resolve) => {
    try {
      const ws = new WebSocket(config.aiBackendUrl);
      const timeout = setTimeout(() => {
        ws.close();
        resolve(false);
      }, config.timeout);
      
      ws.onopen = () => {
        clearTimeout(timeout);
        ws.close();
        resolve(true);
      };
      
      ws.onerror = () => {
        clearTimeout(timeout);
        resolve(false);
      };
    } catch (error) {
      console.warn('⚠️ AI 后端 WebSocket 连接失败:', error);
      resolve(false);
    }
  });
}

// 导出配置常量
export const BACKEND_CONFIG = getBackendConfig();

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.group('🔧 后端服务配置调试信息');
  console.log('环境变量:', {
    VITE_AI_BACKEND_URL: import.meta.env.VITE_AI_BACKEND_URL,
    VITE_AI_BACKEND_ENABLED: import.meta.env.VITE_AI_BACKEND_ENABLED,
    VITE_TASK_BACKEND_URL: import.meta.env.VITE_TASK_BACKEND_URL,
    VITE_TASK_BACKEND_ENABLED: import.meta.env.VITE_TASK_BACKEND_ENABLED
  });
  console.log('解析后的配置:', BACKEND_CONFIG);
  console.groupEnd();
}
