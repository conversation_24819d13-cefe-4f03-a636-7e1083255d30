# 后端服务配置指南

## 📋 概述

本指南详细说明如何配置和部署支持 WebCodecs 智能视频处理的后端服务。系统需要三个主要的后端服务：

1. **主要 API 服务** - AI 聊天、指令处理
2. **WebCodecs 视频处理服务** - 智能分段、音频识别
3. **文件上传服务** - 视频文件上传和管理

## 🏗️ 服务架构

```
前端 (Vue.js)
    ↓
┌─────────────────────────────────────────┐
│              API 网关/负载均衡              │
└─────────────────────────────────────────┘
    ↓                ↓                ↓
┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│  主要 API    │  │ WebCodecs   │  │  文件上传    │
│    服务      │  │  处理服务    │  │    服务      │
│             │  │             │  │             │
│ :8000       │  │ :8080       │  │ :8000       │
└─────────────┘  └─────────────┘  └─────────────┘
```

## ⚙️ 环境配置

### 1. 复制环境配置文件

```bash
# 复制环境配置模板
cp .env.example .env.local

# 编辑配置文件
vim .env.local
```

### 2. 基础配置

```bash
# 主要后端服务地址（AI 聊天、指令处理等）
VITE_API_BASE_URL=http://localhost:8000

# WebCodecs 视频处理服务地址
VITE_VIDEO_API_BASE_URL=https://localhost:49306

# 文件上传服务地址
VITE_UPLOAD_API_BASE_URL=http://localhost:8000
```

### 3. WebCodecs 处理配置

```bash
# JPEG 质量 (1-100，数值越大质量越高)
VITE_WEBCODECS_JPEG_QUALITY=80

# 默认目标帧率
VITE_WEBCODECS_TARGET_FPS=5

# 帧数限制
VITE_WEBCODECS_MIN_FRAME_COUNT=4
VITE_WEBCODECS_MAX_FRAME_COUNT=512
```

## 🚀 服务部署

### 1. 主要 API 服务 (端口 8000)

这是现有的 AI 后端服务，需要支持以下端点：

```
GET  /api/v1/health           # 健康检查
POST /api/v1/ai/chat          # AI 聊天
POST /api/v1/upload/video     # 视频文件上传
```

**部署命令：**
```bash
cd backend
python -m uvicorn main:app --host 0.0.0.0 --port 8000
```

### 2. WebCodecs 视频处理服务 (端口 8080)

这是新的视频处理服务，需要支持以下端点：

```
# 健康检查
GET  /api/v1/health

# 任务管理
POST /api/v1/task/create      # 创建处理任务
GET  /api/v1/task/status      # 获取任务状态
DELETE /api/v1/task/delete    # 删除任务

# 音频处理
POST /api/v1/asr/audio-recognition  # 音频识别

# 分段处理
POST /api/v1/segments/upload-metadata  # 上传分段元数据
POST /api/v1/segments/upload-frames    # 流式上传分段帧

# 章节聚合
POST /api/v1/chapters/aggregate        # 章节聚合处理
```

**部署要求：**
- 必须使用 HTTPS 协议（WebCodecs 要求）
- 支持 HTTP/2 流式上传
- 需要处理大文件上传

**示例部署脚本：**
```bash
# 使用 Node.js + Express
cd video-processing-service
npm install
npm run build
npm start

# 或使用 Python + FastAPI
cd video-processing-service
pip install -r requirements.txt
uvicorn main:app --host 0.0.0.0 --port 8080 --ssl-keyfile key.pem --ssl-certfile cert.pem
```

### 3. HTTPS 证书配置

WebCodecs API 要求 HTTPS 环境，开发环境可以使用自签名证书：

```bash
# 生成自签名证书
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# 在浏览器中信任证书
# Chrome: 访问 https://localhost:49306，点击"高级" -> "继续访问"
```

## 🔍 服务健康检查

系统提供了自动的服务健康检查功能：

### 1. 前端自动检查

```typescript
import { quickHealthCheck, canUseWebCodecs } from '@/utils/service-health-check';

// 检查所有服务
const healthResult = await quickHealthCheck();

// 检查 WebCodecs 可用性
const webCodecsStatus = await canUseWebCodecs();
```

### 2. 手动检查

```bash
# 检查主要 API 服务
curl http://localhost:8000/api/v1/health

# 检查 WebCodecs 服务
curl -k https://localhost:49306/api/v1/health

# 检查文件上传服务
curl http://localhost:8000/api/v1/health
```

## 🐛 故障排除

### 1. WebCodecs 服务不可用

**症状：** 浏览器显示"WebCodecs 服务不可用"

**解决方案：**
```bash
# 检查服务是否运行
netstat -tlnp | grep 8080

# 检查 HTTPS 证书
curl -k https://localhost:49306/api/v1/health

# 检查防火墙设置
sudo ufw allow 8080
```

### 2. CORS 跨域问题

**症状：** 浏览器控制台显示 CORS 错误

**解决方案：**
```javascript
// 后端服务需要配置 CORS
app.use(cors({
  origin: ['http://localhost:3000', 'https://localhost:3000'],
  credentials: true
}));
```

### 3. 文件上传失败

**症状：** 上传大视频文件时失败

**解决方案：**
```bash
# 增加 Nginx 上传限制
client_max_body_size 1000M;

# 增加 Node.js 内存限制
node --max-old-space-size=4096 server.js
```

### 4. WebCodecs API 不支持

**症状：** 浏览器不支持 WebCodecs

**解决方案：**
- 使用 Chrome 94+ 或 Edge 94+
- 确保在 HTTPS 环境或 localhost 下运行
- 启用实验性 Web 平台功能

## 📊 性能优化

### 1. 服务器配置

```bash
# 增加文件描述符限制
ulimit -n 65536

# 优化 TCP 设置
echo 'net.core.somaxconn = 65536' >> /etc/sysctl.conf
sysctl -p
```

### 2. 缓存配置

```javascript
// Redis 缓存配置
const redis = new Redis({
  host: 'localhost',
  port: 6379,
  maxRetriesPerRequest: 3
});
```

### 3. 负载均衡

```nginx
upstream video_processing {
    server localhost:49306;
    server localhost:8081;
    server localhost:8082;
}

server {
    listen 443 ssl http2;
    location /api/v1/ {
        proxy_pass https://video_processing;
    }
}
```

## 🔐 安全配置

### 1. API 密钥管理

```bash
# 生成 API 密钥
VITE_API_KEY=your-secret-api-key

# JWT 密钥
VITE_JWT_SECRET=your-jwt-secret
```

### 2. 请求限制

```javascript
// 速率限制
const rateLimit = require('express-rate-limit');
app.use(rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100 // 限制每个IP 100个请求
}));
```

## 📈 监控和日志

### 1. 日志配置

```bash
# 启用详细日志
VITE_LOG_LEVEL=debug
VITE_DEBUG_WEBCODECS=true
```

### 2. 性能监控

```javascript
// 添加性能监控
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`${req.method} ${req.path} - ${duration}ms`);
  });
  next();
});
```

## ✅ 部署检查清单

- [ ] 所有服务正常启动
- [ ] HTTPS 证书配置正确
- [ ] CORS 跨域配置完成
- [ ] 文件上传限制设置合理
- [ ] 健康检查端点正常响应
- [ ] WebCodecs API 浏览器支持确认
- [ ] 防火墙端口开放
- [ ] 日志和监控配置完成

完成以上配置后，您的 WebCodecs 智能视频处理系统就可以正常运行了！
