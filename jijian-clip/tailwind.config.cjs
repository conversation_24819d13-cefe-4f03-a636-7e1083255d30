/** @type {import('tailwindcss').Config} */
function generateSpacing() {
  const spacing = {};
  for (let i = 1; i <= 400; i++) {
    spacing[i * 0.25] = `${i * 0.25 * 0.25}rem`; // 假设1rem = 16px
  }
  return spacing;
}

module.exports = {
  content: ['./index.html', './src/**/*.{vue,js,ts,jsx,tsx}'],
  darkMode: 'class',
  theme: {
    fontFamily: {
      // 保留原有字体配置
      ping: ['PingFangSC-Regular, PingFang SC'],
      pingm: ['PingFangSC-Medium, PingFang SC'],
      pingb: ['PingFangSC-Semibold, PingFang SC'],

      // 新增统一字体系统
      primary: ['-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', 'Helvetica Neue', 'Helvetica', 'Arial', 'sans-serif'],
      mono: ['SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', 'Consolas', 'Courier New', 'monospace'],
      display: ['SF Pro Display', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'PingFang SC', 'sans-serif'],
    },
    fontSize: {
      // 保留原有尺寸
      xss: '0.5rem',
      xs: '.625rem',
      sm: '0.75rem',
      tiny: '0.875rem',
      base: '1rem',
      bigger: '1.125rem',
      lg: '1.25rem',
      xl: '1.375rem',
      '2xl': '1.5rem',
      '3xl': '1.625rem',
      '4xl': '1.75rem',
      '5xl': '1.875rem',
      '6xl': '2rem',
      '7xl': '2.125rem',

      // 新增统一字体大小系统（使用CSS变量）
      'sys-xs': 'var(--text-xs)',
      'sys-sm': 'var(--text-sm)',
      'sys-base': 'var(--text-base)',
      'sys-md': 'var(--text-md)',
      'sys-lg': 'var(--text-lg)',
      'sys-xl': 'var(--text-xl)',
      'sys-2xl': 'var(--text-2xl)',
    },
    extend: {
      spacing: generateSpacing(),
      height: {
        'account-chart': '10.6rem',
      },
      minHeight: {
        '40': '10rem',
        'card': '17.875rem'
      },
      lineHeight: {
        '12.5': '3.125rem',
      },
      fontFamily: {
        // 保留原有字体配置
        'norm': 'PingFangSC-Regular, PingFang SC;',
        'med': 'PingFangSC-Medium, PingFang SC;',
        'bold': 'PingFangSC-Semibold, PingFang SC;',

        // 扩展统一字体系统
        'sys-primary': 'var(--font-family-primary)',
        'sys-mono': 'var(--font-family-mono)',
        'sys-display': 'var(--font-family-display)',
      },
      fontWeight: {
        'sys-light': 'var(--font-weight-light)',
        'sys-normal': 'var(--font-weight-normal)',
        'sys-medium': 'var(--font-weight-medium)',
        'sys-semibold': 'var(--font-weight-semibold)',
        'sys-bold': 'var(--font-weight-bold)',
      },
      lineHeight: {
        'sys-tight': 'var(--line-height-tight)',
        'sys-normal': 'var(--line-height-normal)',
        'sys-relaxed': 'var(--line-height-relaxed)',
      },
      letterSpacing: {
        'sys-tight': 'var(--letter-spacing-tight)',
        'sys-normal': 'var(--letter-spacing-normal)',
        'sys-wide': 'var(--letter-spacing-wide)',
      },
      borderRadius: {
        'smh': '0.1875rem'
      },
      boxShadow: {
        default: '0px 0px 8px 0px rgba(0,0,0,0.08)',
      },
      zIndex: {
        '-1': '-1',
      },
      colors: {
        // 即剪AI主题色彩 - 继承自268616744450项目
        'jijian-primary': '#7B61FF',
        'jijian-primary-dark': '#6A50E0',
        'jijian-bg-light': '#F5F7FA',
        'jijian-text-dark': '#333333',
        'jijian-text-light': '#666666',
        'jijian-white': '#FFFFFF',

        // 保留原有色彩系统（向后兼容）
        primary: '#7B61FF', // 更新为即剪AI主色
        'true-dark': '#000000',
        'light-dark': '#333333',
        'title-dark': '#393E45',
        'gray': '#999999',
        'des-gray': '#8C8C8C',
        'dark': '#666666',
        'title': '#181818',
        'dark-title': '#1B1B1B',
        'tag-blue': '#1C4FAD',
        'tag-black': '#373737',
        'tag-green': '#3BAE2B',
        'tag-red': '#F34039',
        'tag-gray': '#888888',
        'index-red': '#FF0000',
        'index-orange': '#FF5400',
        'index-yellow': '#FFB700',
        'gray-header': '#F0F2F5',
        'gray-border': '#EDEDED',
        'gray-button': '#FAFAFA',
        'gray-source': '#979797',
      },
      backgroundColor: (theme) => ({
        ...theme('colors'),
        'layout-gray': '#F5F6F8',
        'white': '#FFFFFF',
        'topic-preview': '#D7ECFF',
      }),
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #7B61FF 0%, #6A50E0 100%)',
        'gradient-jijian': 'linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%)',
      },
      animation: {
        // 移除旧的float动画，使用组件内的优化版本
        'fade-in': 'fadeInOptimized 0.6s ease-out',
        'slide-up': 'slideUpOptimized 0.5s ease-out',
        'scale-in': 'scaleInOptimized 0.3s ease-out',
        'pulse-glow': 'pulse-glow 2s ease-in-out infinite',
      },
      keyframes: {
        // 🎯 GPU加速的优化动画
        fadeInOptimized: {
          '0%': { opacity: '0', transform: 'translate3d(0, 20px, 0)' },
          '100%': { opacity: '1', transform: 'translate3d(0, 0, 0)' },
        },
        slideUpOptimized: {
          '0%': { transform: 'translate3d(0, 100%, 0)' },
          '100%': { transform: 'translate3d(0, 0, 0)' },
        },
        scaleInOptimized: {
          '0%': { transform: 'scale3d(0.95, 0.95, 1)', opacity: '0' },
          '100%': { transform: 'scale3d(1, 1, 1)', opacity: '1' },
        },
        'pulse-glow': {
          '0%, 100%': {
            transform: 'scale3d(1, 1, 1)',
            boxShadow: '0 0 0 0 rgba(123, 97, 255, 0.4)'
          },
          '50%': {
            transform: 'scale3d(1.05, 1.05, 1)',
            boxShadow: '0 0 0 10px rgba(123, 97, 255, 0)'
          },
        },
      },
      transitionTimingFunction: {
        'jijian': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      gridTemplateColumns: {
        // Simple 20 column grid
        '20': 'repeat(20, minmax(0, 1fr))',
      },
      screens: {
        sm: '576px',
        md: '768px',
        lg: '992px',
        xl: '1400px',
        '2xl': '1600px',
      },
    },
  }
}
